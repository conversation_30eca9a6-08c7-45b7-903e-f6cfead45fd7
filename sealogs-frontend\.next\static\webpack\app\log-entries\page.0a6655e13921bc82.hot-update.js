"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/ui/risk-analysis/risk-analysis-sheet.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RiskAnalysisSheet: function() { return /* binding */ RiskAnalysisSheet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ RiskAnalysisSheet auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RiskAnalysisSheet(param) {\n    let { open, onOpenChange, onSidebarClose, title, subtitle, checkFields, riskFactors = [], crewMembers = [], selectedAuthor, onAuthorChange, canEdit = true, canDeleteRisks = true, onRiskClick, onAddRiskClick, onRiskDelete, setAllChecked } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if all fields are checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(checkFields.every((field)=>field.checked));\n        }\n    }, [\n        checkFields,\n        setAllChecked\n    ]);\n    const handleRiskClick = (risk)=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onRiskClick) {\n            onRiskClick(risk);\n        }\n    };\n    const handleAddRiskClick = ()=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onAddRiskClick) {\n            onAddRiskClick();\n        }\n    };\n    const handleDeleteRisk = (risk)=>{\n        if (!canEdit || !canDeleteRisks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to delete risks\"\n            });\n            return;\n        }\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const confirmDeleteRisk = ()=>{\n        if (onRiskDelete && riskToDelete) {\n            onRiskDelete(riskToDelete);\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.Sheet, {\n                open: open,\n                onOpenChange: onOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[90vw] sm:w-[60vw]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetTitle, {\n                                children: [\n                                    title,\n                                    \" \",\n                                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-thin\",\n                                        children: subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetBody, {\n                            className: \"grid space-y-0 md:grid-cols-2 gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col min-h-[400px] overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                            className: \"h-full mb-5 border border-dashed border-border rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full p-2 sm:p-5 space-y-2\",\n                                                children: checkFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__.CheckFieldLabel, {\n                                                            id: \"\".concat(field.value, \"-onChangeComplete-\").concat(index),\n                                                            type: \"checkbox\",\n                                                            checked: field.checked,\n                                                            onCheckedChange: (checked)=>{\n                                                                field.handleChange(checked === true);\n                                                            },\n                                                            variant: \"warning\",\n                                                            label: field.label,\n                                                            rightContent: field.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.Popover, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            onClick: (e)=>e.stopPropagation(),\n                                                                            size: \"icon\",\n                                                                            iconOnly: true,\n                                                                            iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"text-curious-blue-900 fill-curious-blue-50\",\n                                                                                size: 24\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 246,\n                                                                                columnNumber: 73\n                                                                            }, void 0),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"View description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 253,\n                                                                                columnNumber: 69\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 65\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 61\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverContent, {\n                                                                        className: \"w-72 p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: field.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 261,\n                                                                                columnNumber: 69\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 65\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 61\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 57\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, \"\".concat(index, \"-\").concat(field.name), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 41\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 29\n                                        }, this),\n                                        crewMembers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Who completed risk\",\n                                            htmlFor: \"author\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__.Combobox, {\n                                                id: \"author\",\n                                                options: crewMembers.map((member)=>{\n                                                    var _member_label, _member_label1;\n                                                    return {\n                                                        ...member,\n                                                        profile: member.profile || {\n                                                            firstName: (_member_label = member.label) === null || _member_label === void 0 ? void 0 : _member_label.split(\" \")[0],\n                                                            surname: (_member_label1 = member.label) === null || _member_label1 === void 0 ? void 0 : _member_label1.split(\" \").slice(1).join(\" \"),\n                                                            avatar: member.avatar || member.profileImage\n                                                        }\n                                                    };\n                                                }),\n                                                value: selectedAuthor,\n                                                placeholder: \"Select crew\",\n                                                onChange: (option)=>{\n                                                    if (onAuthorChange) {\n                                                        onAuthorChange(option);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full col-span-1 min-h-[400px] flex flex-col overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                            className: \"h-full border border-dashed border-border mb-4 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full p-2 sm:p-5\",\n                                                children: riskFactors.length > 0 && riskFactors.map((risk)=>{\n                                                    var _risk_mitigationStrategy_nodes, _risk_mitigationStrategy, _risk_mitigationStrategy1;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4 p-3 rounded-md border border-dashed border-border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"cursor-pointer\",\n                                                                onClick: ()=>handleRiskClick(risk),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    label: risk.title,\n                                                                    className: \"font-medium\",\n                                                                    children: (risk === null || risk === void 0 ? void 0 : risk.impact) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                                                                                className: \"w-full leading-tight text-sm font-normal rounded-lg\",\n                                                                                children: [\n                                                                                    \"Impact:\",\n                                                                                    \" \",\n                                                                                    risk.impact\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                                                                                className: \"w-full leading-tight text-sm font-normal rounded-lg\",\n                                                                                children: [\n                                                                                    \"Probability:\",\n                                                                                    \" \",\n                                                                                    risk.probability,\n                                                                                    \"/10\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 333,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2.5\",\n                                                                children: [\n                                                                    (risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy = risk.mitigationStrategy) === null || _risk_mitigationStrategy === void 0 ? void 0 : (_risk_mitigationStrategy_nodes = _risk_mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.Popover, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    iconOnly: true,\n                                                                                    iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"text-light-blue-vivid-900 fill-light-blue-vivid-50\",\n                                                                                        size: 24\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 356,\n                                                                                        columnNumber: 73\n                                                                                    }, void 0),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"sr-only\",\n                                                                                        children: \"View strategies\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 363,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                    lineNumber: 351,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 349,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverContent, {\n                                                                                className: \"w-72 p-3\",\n                                                                                children: risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy1 = risk.mitigationStrategy) === null || _risk_mitigationStrategy1 === void 0 ? void 0 : _risk_mitigationStrategy1.nodes.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: s.strategy\n                                                                                        }\n                                                                                    }, s.id, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 374,\n                                                                                        columnNumber: 73\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 369,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        onClick: ()=>handleDeleteRisk(risk),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"sr-only\",\n                                                                            children: \"Delete risk\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 57\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(risk.id, \"-risk-analysis\"), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 45\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            onClick: handleAddRiskClick,\n                                            children: \"Add Risk\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetFooter, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"back\",\n                                        iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 43\n                                        }, void 0),\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"primary\",\n                                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                        onClick: onSidebarClose,\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 200,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: confirmDeleteRisk,\n                actionText: \"Delete\",\n                title: \"Delete Risk\",\n                variant: \"warning\",\n                size: \"lg\",\n                position: \"center\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                    children: \"Are you sure you want to delete this risk? This action cannot be undone.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 432,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskAnalysisSheet, \"9XWJG0EiLnNse9bdjtVYd6HNBsc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = RiskAnalysisSheet;\nvar _c;\n$RefreshReg$(_c, \"RiskAnalysisSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx\n"));

/***/ })

});