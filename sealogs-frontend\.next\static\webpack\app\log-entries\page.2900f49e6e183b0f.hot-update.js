"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/ui/risk-analysis/risk-analysis-sheet.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RiskAnalysisSheet: function() { return /* binding */ RiskAnalysisSheet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ RiskAnalysisSheet auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RiskAnalysisSheet(param) {\n    let { open, onOpenChange, onSidebarClose, title, subtitle, checkFields, riskFactors = [], crewMembers = [], selectedAuthor, onAuthorChange, canEdit = true, canDeleteRisks = true, onRiskClick, onAddRiskClick, onRiskDelete, setAllChecked } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if all fields are checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(checkFields.every((field)=>field.checked));\n        }\n    }, [\n        checkFields,\n        setAllChecked\n    ]);\n    const handleRiskClick = (risk)=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onRiskClick) {\n            onRiskClick(risk);\n        }\n    };\n    const handleAddRiskClick = ()=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onAddRiskClick) {\n            onAddRiskClick();\n        }\n    };\n    const handleDeleteRisk = (risk)=>{\n        if (!canEdit || !canDeleteRisks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to delete risks\"\n            });\n            return;\n        }\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const confirmDeleteRisk = ()=>{\n        if (onRiskDelete && riskToDelete) {\n            onRiskDelete(riskToDelete);\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.Sheet, {\n                open: open,\n                onOpenChange: onOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[90vw] sm:w-[60vw]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetTitle, {\n                                children: [\n                                    title,\n                                    \" \",\n                                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-thin\",\n                                        children: subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetBody, {\n                            className: \"grid space-y-0 md:grid-cols-2 gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col min-h-[400px] overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                            className: \"h-full mb-5 border border-dashed border-border rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full p-2 sm:p-5 space-y-2\",\n                                                children: checkFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__.CheckFieldLabel, {\n                                                            id: \"\".concat(field.value, \"-onChangeComplete-\").concat(index),\n                                                            type: \"checkbox\",\n                                                            checked: field.checked,\n                                                            onCheckedChange: (checked)=>{\n                                                                field.handleChange(checked === true);\n                                                            },\n                                                            variant: \"warning\",\n                                                            label: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, \"\".concat(index, \"-\").concat(field.name), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 41\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 29\n                                        }, this),\n                                        crewMembers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Who completed risk\",\n                                            htmlFor: \"author\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__.Combobox, {\n                                                id: \"author\",\n                                                options: crewMembers.map((member)=>{\n                                                    var _member_label, _member_label1;\n                                                    return {\n                                                        ...member,\n                                                        profile: member.profile || {\n                                                            firstName: (_member_label = member.label) === null || _member_label === void 0 ? void 0 : _member_label.split(\" \")[0],\n                                                            surname: (_member_label1 = member.label) === null || _member_label1 === void 0 ? void 0 : _member_label1.split(\" \").slice(1).join(\" \"),\n                                                            avatar: member.avatar || member.profileImage\n                                                        }\n                                                    };\n                                                }),\n                                                value: selectedAuthor,\n                                                placeholder: \"Select crew\",\n                                                onChange: (option)=>{\n                                                    if (onAuthorChange) {\n                                                        onAuthorChange(option);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full col-span-1 min-h-[400px] flex flex-col overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                            className: \"h-full border border-dashed border-border mb-4 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full p-2 sm:p-5\",\n                                                children: riskFactors.length > 0 && riskFactors.map((risk)=>{\n                                                    var _risk_mitigationStrategy_nodes, _risk_mitigationStrategy, _risk_mitigationStrategy1;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4 p-3 rounded-md border border-dashed border-border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"cursor-pointer\",\n                                                                onClick: ()=>handleRiskClick(risk),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    label: risk.title,\n                                                                    className: \"font-medium\",\n                                                                    children: (risk === null || risk === void 0 ? void 0 : risk.impact) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                                                                                className: \"w-full leading-tight text-sm font-normal rounded-lg\",\n                                                                                children: [\n                                                                                    \"Impact:\",\n                                                                                    \" \",\n                                                                                    risk.impact\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                                                                                className: \"w-full leading-tight text-sm font-normal rounded-lg\",\n                                                                                children: [\n                                                                                    \"Probability:\",\n                                                                                    \" \",\n                                                                                    risk.probability,\n                                                                                    \"/10\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2.5\",\n                                                                children: [\n                                                                    (risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy = risk.mitigationStrategy) === null || _risk_mitigationStrategy === void 0 ? void 0 : (_risk_mitigationStrategy_nodes = _risk_mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.Popover, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    iconOnly: true,\n                                                                                    iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"text-light-blue-vivid-900 fill-light-blue-vivid-50\",\n                                                                                        size: 24\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 354,\n                                                                                        columnNumber: 73\n                                                                                    }, void 0),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"sr-only\",\n                                                                                        children: \"View strategies\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                    lineNumber: 349,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverContent, {\n                                                                                className: \"w-72 p-3\",\n                                                                                children: risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy1 = risk.mitigationStrategy) === null || _risk_mitigationStrategy1 === void 0 ? void 0 : _risk_mitigationStrategy1.nodes.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: s.strategy\n                                                                                        }\n                                                                                    }, s.id, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 372,\n                                                                                        columnNumber: 73\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        onClick: ()=>handleDeleteRisk(risk),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"sr-only\",\n                                                                            children: \"Delete risk\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 57\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(risk.id, \"-risk-analysis\"), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 45\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            onClick: handleAddRiskClick,\n                                            children: \"Add Risk\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetFooter, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"back\",\n                                        iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 43\n                                        }, void 0),\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"primary\",\n                                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                        onClick: onSidebarClose,\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 198,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: confirmDeleteRisk,\n                actionText: \"Delete\",\n                title: \"Delete Risk\",\n                variant: \"warning\",\n                size: \"lg\",\n                position: \"center\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                    children: \"Are you sure you want to delete this risk? This action cannot be undone.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 430,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskAnalysisSheet, \"9XWJG0EiLnNse9bdjtVYd6HNBsc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = RiskAnalysisSheet;\nvar _c;\n$RefreshReg$(_c, \"RiskAnalysisSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx\n"));

/***/ })

});