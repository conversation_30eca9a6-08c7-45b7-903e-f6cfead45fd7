"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/risk-evaluations/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/forms/risk-analysis.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/towingChecklist */ \"(app-pages-browser)/./src/app/offline/models/towingChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/uniqBy */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqBy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// Import React and hooks\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Risk Analysis components\n\nfunction RiskAnalysis(param) {\n    let { selectedEvent = false, onSidebarClose, logBookConfig, currentTrip, crewMembers = false, towingChecklistID = 0, setTowingChecklistID, offline = false, setAllChecked, open = false, onOpenChange, noSheet = false } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = parseInt((_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : \"0\");\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Using setUpdateStrategy but not reading updateStrategy\n    const [, setUpdateStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Unused state variables commented out\n    // const [strategyEditor, setstrategyEditor] = useState<any>(false)\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Using setRecommendedstrategy but not reading recommendedstrategy\n    const [, setRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_risks, setEdit_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_risks, setDelete_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const logBookEntryModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const towingChecklistModel = new _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const riskFactorModel = new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const crewMemberModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const mitigationStrategyModel = new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_RISK\", permissions)) {\n                setEdit_risks(true);\n            } else {\n                setEdit_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"DELETE_RISK\", permissions)) {\n                setDelete_risks(true);\n            } else {\n                setDelete_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [getSectionCrewMembers_LogBookEntrySection, // Unused loading state\n    {}] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                ...members,\n                ...crewMembers\n            ], \"value\"));\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        if (+master.value > 0) {\n            if (Array.isArray(members)) {\n                setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                    ...members,\n                    master\n                ], \"value\"));\n            } else {\n                setMembers([\n                    master\n                ]);\n            }\n        }\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>{\n            return node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\";\n        });\n        if (sections) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if ((sectionIDs === null || sectionIDs === void 0 ? void 0 : sectionIDs.length) > 0) {\n                if (offline) {\n                    const data = await crewMemberModel.getByIds(sectionIDs);\n                    const crewMembers = data.map((member)=>{\n                        var _member_crewMember_firstName, _member_crewMember_surname;\n                        return {\n                            label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                            value: member.crewMember.id\n                        };\n                    });\n                    if (Array.isArray(members)) {\n                        setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                            ...members,\n                            ...crewMembers\n                        ], \"value\"));\n                    } else {\n                        setMembers(crewMembers);\n                    }\n                } else {\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: {\n                                id: {\n                                    in: sectionIDs\n                                }\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    if (logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            const members = crewMembers.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setMembers(members);\n        }\n    }, [\n        crewMembers\n    ]);\n    const handleTaskingRiskFieldChange = (field)=>async (check)=>{\n            if (!editTaskingRisk || !edit_risks) {\n                toast({\n                    title: \"Permission Error\",\n                    description: \"You do not have permission to edit this section\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setRiskBuffer({\n                ...riskBuffer,\n                [field]: check ? \"on\" : \"off\"\n            });\n            if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n                if (offline) {\n                    const data = await towingChecklistModel.save({\n                        id: riskAnalysis.id,\n                        [field]: check ? true : false\n                    });\n                    const towingChecklistData = await towingChecklistModel.getById(data.id);\n                    setRiskAnalysis(towingChecklistData);\n                } else {\n                    updateTowingChecklist({\n                        variables: {\n                            input: {\n                                id: riskAnalysis.id,\n                                [field]: check ? true : false\n                            }\n                        }\n                    });\n                }\n            }\n        };\n    const [updateTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateTowingChecklist, {\n        onCompleted: (data)=>{\n            getRiskAnalysis({\n                variables: {\n                    id: data.updateTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const fields = [\n        {\n            name: \"ConductSAP\",\n            label: \"Conduct SAP\",\n            value: \"conductSAP\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.conductSAP) ? riskBuffer.conductSAP === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.conductSAP,\n            handleChange: handleTaskingRiskFieldChange(\"conductSAP\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Conduct SAP prior to approaching the vessel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 288,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"InvestigateNatureOfIssue\",\n            label: \"Investigate nature of the issue\",\n            value: \"investigateNatureOfIssue\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.investigateNatureOfIssue) ? riskBuffer.investigateNatureOfIssue === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.investigateNatureOfIssue,\n            handleChange: handleTaskingRiskFieldChange(\"investigateNatureOfIssue\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ascertain the nature of the problem, any damage, or taking on water.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Does a crew member need to go on board the other vessel to assist?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 308,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"EveryoneOnBoardOk\",\n            label: \"Everyone on board ok?\",\n            value: \"everyoneOnBoardOk\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.everyoneOnBoardOk) ? riskBuffer.everyoneOnBoardOk === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.everyoneOnBoardOk,\n            handleChange: handleTaskingRiskFieldChange(\"everyoneOnBoardOk\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check how many people are aboard, ensure everyone is accounted for.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for injuries or medical assistance required.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 329,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"RudderToMidshipsAndTrimmed\",\n            label: \"Rudder to midships and trimmed appropriately\",\n            value: \"rudderToMidshipsAndTrimmed\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.rudderToMidshipsAndTrimmed) ? riskBuffer.rudderToMidshipsAndTrimmed === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.rudderToMidshipsAndTrimmed,\n            handleChange: handleTaskingRiskFieldChange(\"rudderToMidshipsAndTrimmed\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check steering isn’t impaired in any way and have the rudder secured amidships or have the vessel steer for the stern of CRV.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check the vessel is optimally trimmed for towing.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 351,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LifejacketsOn\",\n            label: \"Lifejackets on\",\n            value: \"lifejacketsOn\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lifejacketsOn) ? riskBuffer.lifejacketsOn === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lifejacketsOn,\n            handleChange: handleTaskingRiskFieldChange(\"lifejacketsOn\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Request that everyone wears a lifejacket.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 370,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"CommunicationsEstablished\",\n            label: \"Communications Established\",\n            value: \"communicationsEstablished\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.communicationsEstablished) ? riskBuffer.communicationsEstablished === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.communicationsEstablished,\n            handleChange: handleTaskingRiskFieldChange(\"communicationsEstablished\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that communications have been established and checked prior to beginning the tow, i.e., VHF, hand signals, and/or light signals if the tow is to be conducted at night.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure there is agreement on where to tow the vessel to.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 386,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"SecureAndSafeTowing\",\n            label: \"Secure and safe towing\",\n            value: \"secureAndSafeTowing\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.secureAndSafeTowing) ? riskBuffer.secureAndSafeTowing === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.secureAndSafeTowing,\n            handleChange: handleTaskingRiskFieldChange(\"secureAndSafeTowing\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Towline securely attached\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure everything on board is stowed and secure.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm waterline length/cruising speed of the vessel (safe tow speed).\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm attachment points for the towline.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm that the towline is securely attached.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that no one on the other vessel is in close proximity to the towline before commencing the tow.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Turn on CRV towing lights and other vessel’s navigation lights.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Post towline lookout with responsibility for quick release of the tow / must carry or have a knife handy.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 408,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    const createOfflineTowingChecklist = async ()=>{\n        var _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setTowingChecklistID(+data.id);\n        await taskingModel.save({\n            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n            towingChecklistID: +data.id\n        });\n        const towingChecklistData = await towingChecklistModel.getById(data.id);\n        setRiskAnalysis(towingChecklistData);\n    };\n    const offlineGetRiskAnalysis = async ()=>{\n        var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n        setRiskAnalysis(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEvent || towingChecklistID > 0) {\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id) > 0 || towingChecklistID > 0) {\n                if (offline) {\n                    offlineGetRiskAnalysis();\n                } else {\n                    var _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                    getRiskAnalysis({\n                        variables: {\n                            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id\n                        }\n                    });\n                }\n            } else {\n                if (offline) {\n                    createOfflineTowingChecklist();\n                } else {\n                    createTowingChecklist({\n                        variables: {\n                            input: {}\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent,\n        towingChecklistID\n    ]);\n    const offlineMount = async ()=>{\n        var _Array_from;\n        const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n        const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _Array_from, _data_readRiskFactors_nodes;\n            const risks = (_Array_from = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.TowingChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskAnalysis(data.readOneTowingChecklist);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateTowingChecklist, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_Tasking;\n            setTowingChecklistID(+data.createTowingChecklist.id);\n            updateEvent({\n                variables: {\n                    input: {\n                        id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n                        towingChecklistID: +data.createTowingChecklist.id\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: data.createTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_Tasking, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const updateRiskAnalysisMember = async (memberID)=>{\n        if (!editTaskingRisk || !edit_risks) {\n            toast({\n                title: \"Permission Error\",\n                description: \"You do not have permission to edit this section\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (offline) {\n            const data = await towingChecklistModel.save({\n                id: riskAnalysis.id,\n                memberID: memberID\n            });\n            const towingChecklistData = await towingChecklistModel.getById(data.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateTowingChecklist({\n                variables: {\n                    input: {\n                        id: riskAnalysis.id,\n                        memberID: memberID\n                    }\n                }\n            });\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            if (offline) {\n                var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n                await riskFactorModel.save({\n                    id: currentRisk.id,\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _Array_from1, _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                await riskFactorModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                    vesselID: vesselID\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from1 = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from1 === void 0 ? void 0 : _Array_from1.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                            vesselID: vesselID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    // This function is not used directly in the component but is kept for reference\n    // and potential future use\n    // const handleCreateRisk = (inputValue: any) => {\n    //     setCurrentRisk({\n    //         ...currentRisk,\n    //         title: inputValue,\n    //     })\n    //     setRiskValue({ value: inputValue, label: inputValue })\n    //     if (allRisks) {\n    //         const risk = [...allRisks, { value: inputValue, label: inputValue }]\n    //         setAllRisks(risk)\n    //     } else {\n    //         setAllRisks([{ value: inputValue, label: inputValue }])\n    //     }\n    // }\n    const handleDeleteRisk = async (riskToDelete)=>{\n        if (offline) {\n            var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            await riskFactorModel.save({\n                id: riskToDelete.id,\n                towingChecklistID: 0,\n                vesselID: 0\n            });\n            setOpenRiskDialog(false);\n            const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n            const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data);\n            const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        towingChecklistID: 0,\n                        vesselID: 0\n                    }\n                }\n            });\n        }\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.length > 0) {\n            if (currentStrategies.find((s)=>s.id === strategy.id)) {\n                setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n            } else {\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    strategy\n                ]);\n            }\n        } else {\n            setCurrentStrategies([\n                strategy\n            ]);\n        }\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await mitigationStrategyModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    strategy: content\n                });\n                const newStrategies = [\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ];\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: {\n                        nodes: newStrategies\n                    }\n                });\n                setCurrentStrategies(newStrategies);\n                setContent(\"\");\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const offlineGetLogBookEntryByID = async ()=>{\n        const logbook = await logBookEntryModel.getById(logentryID);\n        handleSetLogbook(logbook);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineGetLogBookEntryByID();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (members && riskAnalysis) {\n            const member = members.find((member)=>member.value == riskAnalysis.member.id);\n            setSelectedAuthor(member);\n        }\n    }, [\n        members,\n        riskAnalysis\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAllChecked(fields.every((field)=>field.checked));\n    }, [\n        fields\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    onSidebarClose();\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Towing\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedAuthor,\n                onAuthorChange: (value)=>{\n                    setSelectedAuthor(value);\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editTaskingRisk && edit_risks,\n                canDeleteRisks: editTaskingRisk && delete_risks,\n                onRiskClick: (risk)=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                },\n                onRiskDelete: (risk)=>{\n                    handleDeleteRisk(risk);\n                },\n                setAllChecked: setAllChecked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 977,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskDialog, {\n                open: openRiskDialog,\n                onOpenChange: setOpenRiskDialog,\n                onSave: handleSaveRisk,\n                currentRisk: currentRisk,\n                riskOptions: allRisks || [],\n                riskValue: riskValue,\n                onRiskValueChange: handleRiskValue,\n                riskImpacts: riskImpacts,\n                onRiskImpactChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        impact: value === null || value === void 0 ? void 0 : value.value\n                    }),\n                onRiskProbabilityChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        probability: value\n                    }),\n                currentStrategies: currentStrategies,\n                content: content,\n                onAddStrategyClick: ()=>setOpenRecommendedstrategy(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1046,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.StrategyDialog, {\n                open: openRecommendedstrategy,\n                onOpenChange: setOpenRecommendedstrategy,\n                onSave: handleNewStrategy,\n                currentRisk: currentRisk,\n                recommendedStrategies: recommendedStratagies,\n                currentStrategies: currentStrategies,\n                onStrategySelect: (strategy)=>{\n                    setRecommendedstrategy(strategy);\n                    handleSetCurrentStrategies(strategy);\n                    setCurrentRisk({\n                        ...currentRisk,\n                        mitigationStrategy: strategy\n                    });\n                    setUpdateStrategy(false);\n                },\n                content: content,\n                onEditorChange: handleEditorChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1071,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n        lineNumber: 976,\n        columnNumber: 9\n    }, this);\n}\n_s(RiskAnalysis, \"kTMg8DIdMX+xx1T+4DpMoUOFIJc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = RiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"RiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\n"));

/***/ })

});