"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx":
/*!*****************************************************************!*\
  !*** ./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BarCrossingRiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_barCrossingChecklist__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/barCrossingChecklist */ \"(app-pages-browser)/./src/app/offline/models/barCrossingChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_eventType_BarCrossing__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/eventType_BarCrossing */ \"(app-pages-browser)/./src/app/offline/models/eventType_BarCrossing.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/uniqBy */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqBy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction BarCrossingRiskAnalysis(param) {\n    let { selectedEvent = false, onSidebarClose, barCrossingChecklistID = 0, setBarCrossingChecklistID, offline = false, setAllChecked, crewMembers = false, logBookConfig, currentTrip, open = false, onOpenChange } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const logentryID = parseInt((_searchParams_get1 = searchParams.get(\"logentryID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : \"0\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [updateStrategy, setUpdateStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recommendedstrategy, setRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [creatingBarCrossingChecklist, setCreatingBarCrossingChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_risks, setEdit_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_risks, setDelete_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editBarCrossingRisk, setEditBarCrossingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const logBookEntryModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const barCrossingChecklistModel = new _app_offline_models_barCrossingChecklist__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const riskFactorModel = new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const crewMemberModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const barCrossingModel = new _app_offline_models_eventType_BarCrossing__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const mitigationStrategyModel = new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const init_permissions = ()=>{\n        if (permissions) {\n            setEdit_risks((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_RISK\", permissions));\n            setDelete_risks((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"DELETE_RISK\", permissions));\n            setEditBarCrossingRisk((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions));\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [getSectionCrewMembers_LogBookEntrySection, { loading: crewMembersLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                ...members || [],\n                ...crewMembers\n            ], \"value\"));\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        if (+master.value > 0) {\n            if (Array.isArray(members)) {\n                setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                    ...members || [],\n                    master\n                ], \"value\"));\n            } else {\n                setMembers([\n                    master\n                ]);\n            }\n        }\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\");\n        if (sections && sections.length > 0) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if (offline) {\n                const data = await crewMemberModel.getByIds(sectionIDs);\n                const crewMembers = data.map((member)=>{\n                    var _member_crewMember_firstName, _member_crewMember_surname;\n                    return {\n                        label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                        value: member.crewMember.id\n                    };\n                });\n                setMembers(Array.isArray(members) ? [\n                    ...members || [],\n                    ...crewMembers\n                ] : crewMembers);\n            } else {\n                getSectionCrewMembers_LogBookEntrySection({\n                    variables: {\n                        filter: {\n                            id: {\n                                in: sectionIDs\n                            }\n                        }\n                    }\n                });\n            }\n        }\n    };\n    if (logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            const mapped = crewMembers.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setMembers(mapped);\n        }\n    }, [\n        crewMembers\n    ]);\n    const handleBCRAFieldChange = (field)=>async (check)=>{\n            if (!editBarCrossingRisk || !edit_risks) {\n                toast({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"You do not have permission to edit this section\"\n                });\n                return;\n            }\n            setRiskBuffer({\n                ...riskBuffer,\n                [field]: check ? \"on\" : \"off\"\n            });\n            if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n                if (offline) {\n                    await barCrossingChecklistModel.save({\n                        id: riskAnalysis.id,\n                        [field]: check\n                    });\n                } else {\n                    updateBarCrossingChecklist({\n                        variables: {\n                            input: {\n                                id: riskAnalysis.id,\n                                [field]: check\n                            }\n                        }\n                    });\n                }\n            }\n        };\n    const [updateBarCrossingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateBarCrossingChecklist, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    // Fix the fields by explicitly adding a \"checked\" key.\n    const fields = [\n        {\n            name: \"StopAssessPlan\",\n            label: \"Stopped, Assessed, Planned\",\n            value: \"stopAssessPlan\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.stopAssessPlan) ? riskBuffer.stopAssessPlan === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.stopAssessPlan,\n            handleChange: handleBCRAFieldChange(\"stopAssessPlan\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Pause before crossing to evaluate conditions and create a detailed crossing plan.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 241,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"CrewBriefing\",\n            label: \"Briefed crew on crossing\",\n            value: \"crewBriefing\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.crewBriefing) ? riskBuffer.crewBriefing === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.crewBriefing,\n            handleChange: handleBCRAFieldChange(\"crewBriefing\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Inform the crew about the crossing plan and any potential hazards.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 258,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"Weather\",\n            label: \"Weather, tide, bar conditions checked as suitable for crossing\",\n            value: \"weather\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.weather) ? riskBuffer.weather === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.weather,\n            handleChange: handleBCRAFieldChange(\"weather\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Verify that weather, tide, and bar conditions are favorable and safe for crossing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 275,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"Stability\",\n            label: \"Adequate stability checked\",\n            value: \"stability\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.stability) ? riskBuffer.stability === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.stability,\n            handleChange: handleBCRAFieldChange(\"stability\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Ensure the vessel is stable enough to handle the crossing without capsizing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 292,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LifeJackets\",\n            label: \"Lifejackets on\",\n            value: \"lifeJackets\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lifeJackets) ? riskBuffer.lifeJackets === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lifeJackets,\n            handleChange: handleBCRAFieldChange(\"lifeJackets\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Instruct crew to wear lifejackets if conditions are rough or challenging.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 309,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"WaterTightness\",\n            label: \"Water tightness checked\",\n            value: \"waterTightness\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.waterTightness) ? riskBuffer.waterTightness === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.waterTightness,\n            handleChange: handleBCRAFieldChange(\"waterTightness\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Confirm that all hatches and doors are secured to prevent water ingress.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 326,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LookoutPosted\",\n            label: \"Lookout posted\",\n            value: \"lookoutPosted\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lookoutPosted) ? riskBuffer.lookoutPosted === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lookoutPosted,\n            handleChange: handleBCRAFieldChange(\"lookoutPosted\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Assign a crew member to watch for hazards or changes in conditions during the crossing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 343,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(fields.every((field)=>field.checked));\n        }\n    }, [\n        fields\n    ]);\n    const offlineGetRiskAnalysis = async ()=>{\n        var _selectedEvent_eventType_BarCrossing;\n        const data = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.barCrossingChecklistID);\n        setRiskAnalysis(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEvent || barCrossingChecklistID > 0) {\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id) > 0 || barCrossingChecklistID > 0) {\n                if (offline) {\n                    offlineGetRiskAnalysis();\n                } else {\n                    var _selectedEvent_eventType_BarCrossing_barCrossingChecklist1, _selectedEvent_eventType_BarCrossing1;\n                    getRiskAnalysis({\n                        variables: {\n                            id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist1 = _selectedEvent_eventType_BarCrossing1.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist1.id\n                        }\n                    });\n                }\n            } else {\n                if (!creatingBarCrossingChecklist) {\n                    setCreatingBarCrossingChecklist(true);\n                }\n            }\n        } else {\n            if (!creatingBarCrossingChecklist) {\n                setCreatingBarCrossingChecklist(true);\n            }\n        }\n    }, [\n        selectedEvent,\n        barCrossingChecklistID\n    ]);\n    const createOfflineBarCrossingChecklist = async ()=>{\n        var _selectedEvent_eventType_BarCrossing;\n        const data = await barCrossingChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)()\n        });\n        setBarCrossingChecklistID(+data.id);\n        if (barCrossingChecklistID > 0 || (selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.id)) {\n            var _selectedEvent_eventType_BarCrossing1;\n            await barCrossingModel.save({\n                id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing1.id,\n                barCrossingChecklistID: +data.id\n            });\n        }\n        const barCrossingChecklistData = await barCrossingChecklistModel.getById(data.id);\n        setRiskAnalysis(barCrossingChecklistData);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (creatingBarCrossingChecklist) {\n            if (offline) {\n                createOfflineBarCrossingChecklist();\n            } else {\n                createBarCrossingChecklist({\n                    variables: {\n                        input: {}\n                    }\n                });\n            }\n        }\n    }, [\n        creatingBarCrossingChecklist\n    ]);\n    const offlineMount = async ()=>{\n        const data = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n        const risks = Array.from(new Set(data.map((risk)=>risk.title))).map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readRiskFactors_nodes;\n            const risks = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title))).map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.BarCrossingChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskAnalysis(data.readOneBarCrossingChecklist);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createBarCrossingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateBarCrossingChecklist, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_BarCrossing;\n            setBarCrossingChecklistID(+data.createBarCrossingChecklist.id);\n            if (barCrossingChecklistID > 0 || (selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.id)) {\n                var _selectedEvent_eventType_BarCrossing1;\n                updateEvent({\n                    variables: {\n                        input: {\n                            id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing1.id,\n                            barCrossingChecklistID: +data.createBarCrossingChecklist.id\n                        }\n                    }\n                });\n            }\n            getRiskAnalysis({\n                variables: {\n                    id: data.createBarCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_BarCrossing, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const updateRiskAnalysisMember = async (memberID)=>{\n        if (!editBarCrossingRisk || !edit_risks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        setRiskBuffer({\n            ...riskBuffer,\n            memberID\n        });\n        if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n            if (offline) {\n                await barCrossingChecklistModel.save({\n                    id: riskAnalysis.id,\n                    memberID\n                });\n            } else {\n                updateBarCrossingChecklist({\n                    variables: {\n                        input: {\n                            id: riskAnalysis.id,\n                            memberID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            if (offline) {\n                var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n                await riskFactorModel.save({\n                    id: currentRisk.id,\n                    type: \"BarCrossingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                });\n                setOpenRiskDialog(false);\n                const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n                const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(riskFactorData);\n                const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id);\n                setRiskAnalysis(barCrossingChecklistData);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"BarCrossingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                await riskFactorModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    type: \"BarCrossingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                    vesselID\n                });\n                setOpenRiskDialog(false);\n                const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n                const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(riskFactorData);\n                const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id);\n                setRiskAnalysis(barCrossingChecklistData);\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"BarCrossingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                            vesselID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateMitigationStrategy, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateRiskFactor, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            const items = Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                }))));\n            setRecommendedStratagies(items);\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const handleCreateRisk = (inputValue)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: inputValue\n        });\n        setRiskValue({\n            value: inputValue,\n            label: inputValue\n        });\n        if (allRisks) {\n            setAllRisks([\n                ...allRisks,\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        } else {\n            setAllRisks([\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        }\n    };\n    const handleDeleteRisk = async ()=>{\n        if (offline) {\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            await riskFactorModel.save({\n                id: riskToDelete.id,\n                barCrossingChecklistID: 0,\n                vesselID: 0\n            });\n            setOpenRiskDialog(false);\n            const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n            const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(riskFactorData);\n            const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id);\n            setRiskAnalysis(barCrossingChecklistData);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        barCrossingChecklistID: 0,\n                        vesselID: 0\n                    }\n                }\n            });\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.find((s)=>s.id === strategy.id)) {\n            setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n        } else {\n            setCurrentStrategies([\n                ...currentStrategies,\n                strategy\n            ]);\n        }\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await mitigationStrategyModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    strategy: content\n                });\n                const newStrategies = [\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ];\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: {\n                        nodes: newStrategies\n                    }\n                });\n                setCurrentStrategies(newStrategies);\n                setContent(\"\");\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const offlineUseEffect = async ()=>{\n        const logbook = await logBookEntryModel.getById(logentryID);\n        handleSetLogbook(logbook);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineUseEffect();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (members && riskAnalysis) {\n            const member = members.find((member)=>member.value == riskAnalysis.member.id);\n            setSelectedAuthor(member);\n        }\n    }, [\n        members,\n        riskAnalysis\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    onSidebarClose();\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Bar Crossing\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedAuthor,\n                onAuthorChange: (value)=>{\n                    setSelectedAuthor(value);\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editBarCrossingRisk && edit_risks,\n                canDeleteRisks: editBarCrossingRisk && delete_risks,\n                onRiskClick: (risk)=>{\n                    if (!editBarCrossingRisk || !edit_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editBarCrossingRisk || !edit_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                },\n                onRiskDelete: (risk)=>{\n                    if (!editBarCrossingRisk || !delete_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to delete risks\"\n                        });\n                        return;\n                    }\n                    setRiskToDelete(risk);\n                    handleDeleteRisk();\n                },\n                setAllChecked: setAllChecked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 893,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskDialog, {\n                open: openRiskDialog,\n                onOpenChange: setOpenRiskDialog,\n                currentRisk: currentRisk,\n                onSave: handleSaveRisk,\n                riskOptions: allRisks,\n                riskValue: riskValue,\n                onRiskValueChange: handleRiskValue,\n                riskImpacts: riskImpacts,\n                onRiskImpactChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        impact: value === null || value === void 0 ? void 0 : value.value\n                    }),\n                onRiskProbabilityChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        probability: value\n                    }),\n                currentStrategies: currentStrategies,\n                content: content,\n                onAddStrategyClick: ()=>setOpenRecommendedstrategy(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 971,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.StrategyDialog, {\n                open: openRecommendedstrategy,\n                onOpenChange: setOpenRecommendedstrategy,\n                onSave: handleNewStrategy,\n                currentRisk: currentRisk,\n                recommendedStrategies: recommendedStratagies,\n                currentStrategies: currentStrategies,\n                onStrategySelect: (strategy)=>{\n                    setRecommendedstrategy(strategy);\n                    handleSetCurrentStrategies(strategy);\n                    setCurrentRisk({\n                        ...currentRisk,\n                        mitigationStrategy: strategy\n                    });\n                    setUpdateStrategy(false);\n                },\n                content: content,\n                onEditorChange: handleEditorChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 996,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n        lineNumber: 892,\n        columnNumber: 9\n    }, this);\n}\n_s(BarCrossingRiskAnalysis, \"FtwdefzqufZudHvYVo2QsVqONSU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = BarCrossingRiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"BarCrossingRiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/forms/risk-analysis.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/towingChecklist */ \"(app-pages-browser)/./src/app/offline/models/towingChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/uniqBy */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqBy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// Import React and hooks\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Risk Analysis components\n\nfunction RiskAnalysis(param) {\n    let { selectedEvent = false, onSidebarClose, logBookConfig, currentTrip, crewMembers = false, towingChecklistID = 0, setTowingChecklistID, offline = false, setAllChecked, open = false, onOpenChange } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = parseInt((_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : \"0\");\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Using setUpdateStrategy but not reading updateStrategy\n    const [, setUpdateStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Unused state variables commented out\n    // const [strategyEditor, setstrategyEditor] = useState<any>(false)\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Using setRecommendedstrategy but not reading recommendedstrategy\n    const [, setRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_risks, setEdit_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_risks, setDelete_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const logBookEntryModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const towingChecklistModel = new _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const riskFactorModel = new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const crewMemberModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const mitigationStrategyModel = new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_RISK\", permissions)) {\n                setEdit_risks(true);\n            } else {\n                setEdit_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"DELETE_RISK\", permissions)) {\n                setDelete_risks(true);\n            } else {\n                setDelete_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [getSectionCrewMembers_LogBookEntrySection, // Unused loading state\n    {}] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                ...members,\n                ...crewMembers\n            ], \"value\"));\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        if (+master.value > 0) {\n            if (Array.isArray(members)) {\n                setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                    ...members,\n                    master\n                ], \"value\"));\n            } else {\n                setMembers([\n                    master\n                ]);\n            }\n        }\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>{\n            return node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\";\n        });\n        if (sections) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if ((sectionIDs === null || sectionIDs === void 0 ? void 0 : sectionIDs.length) > 0) {\n                if (offline) {\n                    const data = await crewMemberModel.getByIds(sectionIDs);\n                    const crewMembers = data.map((member)=>{\n                        var _member_crewMember_firstName, _member_crewMember_surname;\n                        return {\n                            label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                            value: member.crewMember.id\n                        };\n                    });\n                    if (Array.isArray(members)) {\n                        setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                            ...members,\n                            ...crewMembers\n                        ], \"value\"));\n                    } else {\n                        setMembers(crewMembers);\n                    }\n                } else {\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: {\n                                id: {\n                                    in: sectionIDs\n                                }\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    if (logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            const members = crewMembers.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setMembers(members);\n        }\n    }, [\n        crewMembers\n    ]);\n    const handleTaskingRiskFieldChange = (field)=>async (check)=>{\n            if (!editTaskingRisk || !edit_risks) {\n                toast({\n                    title: \"Permission Error\",\n                    description: \"You do not have permission to edit this section\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setRiskBuffer({\n                ...riskBuffer,\n                [field]: check ? \"on\" : \"off\"\n            });\n            if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n                if (offline) {\n                    const data = await towingChecklistModel.save({\n                        id: riskAnalysis.id,\n                        [field]: check ? true : false\n                    });\n                    const towingChecklistData = await towingChecklistModel.getById(data.id);\n                    setRiskAnalysis(towingChecklistData);\n                } else {\n                    updateTowingChecklist({\n                        variables: {\n                            input: {\n                                id: riskAnalysis.id,\n                                [field]: check ? true : false\n                            }\n                        }\n                    });\n                }\n            }\n        };\n    const [updateTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateTowingChecklist, {\n        onCompleted: (data)=>{\n            getRiskAnalysis({\n                variables: {\n                    id: data.updateTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const fields = [\n        {\n            name: \"ConductSAP\",\n            label: \"Conduct SAP\",\n            value: \"conductSAP\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.conductSAP) ? riskBuffer.conductSAP === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.conductSAP,\n            handleChange: handleTaskingRiskFieldChange(\"conductSAP\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Conduct SAP prior to approaching the vessel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 286,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"InvestigateNatureOfIssue\",\n            label: \"Investigate nature of the issue\",\n            value: \"investigateNatureOfIssue\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.investigateNatureOfIssue) ? riskBuffer.investigateNatureOfIssue === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.investigateNatureOfIssue,\n            handleChange: handleTaskingRiskFieldChange(\"investigateNatureOfIssue\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ascertain the nature of the problem, any damage, or taking on water.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Does a crew member need to go on board the other vessel to assist?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 306,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"EveryoneOnBoardOk\",\n            label: \"Everyone on board ok?\",\n            value: \"everyoneOnBoardOk\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.everyoneOnBoardOk) ? riskBuffer.everyoneOnBoardOk === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.everyoneOnBoardOk,\n            handleChange: handleTaskingRiskFieldChange(\"everyoneOnBoardOk\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check how many people are aboard, ensure everyone is accounted for.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for injuries or medical assistance required.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 327,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"RudderToMidshipsAndTrimmed\",\n            label: \"Rudder to midships and trimmed appropriately\",\n            value: \"rudderToMidshipsAndTrimmed\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.rudderToMidshipsAndTrimmed) ? riskBuffer.rudderToMidshipsAndTrimmed === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.rudderToMidshipsAndTrimmed,\n            handleChange: handleTaskingRiskFieldChange(\"rudderToMidshipsAndTrimmed\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check steering isn’t impaired in any way and have the rudder secured amidships or have the vessel steer for the stern of CRV.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check the vessel is optimally trimmed for towing.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 349,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LifejacketsOn\",\n            label: \"Lifejackets on\",\n            value: \"lifejacketsOn\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lifejacketsOn) ? riskBuffer.lifejacketsOn === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lifejacketsOn,\n            handleChange: handleTaskingRiskFieldChange(\"lifejacketsOn\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Request that everyone wears a lifejacket.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 368,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"CommunicationsEstablished\",\n            label: \"Communications Established\",\n            value: \"communicationsEstablished\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.communicationsEstablished) ? riskBuffer.communicationsEstablished === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.communicationsEstablished,\n            handleChange: handleTaskingRiskFieldChange(\"communicationsEstablished\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that communications have been established and checked prior to beginning the tow, i.e., VHF, hand signals, and/or light signals if the tow is to be conducted at night.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure there is agreement on where to tow the vessel to.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 384,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"SecureAndSafeTowing\",\n            label: \"Secure and safe towing\",\n            value: \"secureAndSafeTowing\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.secureAndSafeTowing) ? riskBuffer.secureAndSafeTowing === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.secureAndSafeTowing,\n            handleChange: handleTaskingRiskFieldChange(\"secureAndSafeTowing\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Towline securely attached\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure everything on board is stowed and secure.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm waterline length/cruising speed of the vessel (safe tow speed).\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm attachment points for the towline.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm that the towline is securely attached.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that no one on the other vessel is in close proximity to the towline before commencing the tow.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Turn on CRV towing lights and other vessel’s navigation lights.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Post towline lookout with responsibility for quick release of the tow / must carry or have a knife handy.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 406,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    const createOfflineTowingChecklist = async ()=>{\n        var _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setTowingChecklistID(+data.id);\n        await taskingModel.save({\n            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n            towingChecklistID: +data.id\n        });\n        const towingChecklistData = await towingChecklistModel.getById(data.id);\n        setRiskAnalysis(towingChecklistData);\n    };\n    const offlineGetRiskAnalysis = async ()=>{\n        var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n        setRiskAnalysis(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEvent || towingChecklistID > 0) {\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id) > 0 || towingChecklistID > 0) {\n                if (offline) {\n                    offlineGetRiskAnalysis();\n                } else {\n                    var _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                    getRiskAnalysis({\n                        variables: {\n                            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id\n                        }\n                    });\n                }\n            } else {\n                if (offline) {\n                    createOfflineTowingChecklist();\n                } else {\n                    createTowingChecklist({\n                        variables: {\n                            input: {}\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent,\n        towingChecklistID\n    ]);\n    const offlineMount = async ()=>{\n        var _Array_from;\n        const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n        const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _Array_from, _data_readRiskFactors_nodes;\n            const risks = (_Array_from = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.TowingChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskAnalysis(data.readOneTowingChecklist);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateTowingChecklist, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_Tasking;\n            setTowingChecklistID(+data.createTowingChecklist.id);\n            updateEvent({\n                variables: {\n                    input: {\n                        id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n                        towingChecklistID: +data.createTowingChecklist.id\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: data.createTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_Tasking, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const updateRiskAnalysisMember = async (memberID)=>{\n        if (!editTaskingRisk || !edit_risks) {\n            toast({\n                title: \"Permission Error\",\n                description: \"You do not have permission to edit this section\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (offline) {\n            const data = await towingChecklistModel.save({\n                id: riskAnalysis.id,\n                memberID: memberID\n            });\n            const towingChecklistData = await towingChecklistModel.getById(data.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateTowingChecklist({\n                variables: {\n                    input: {\n                        id: riskAnalysis.id,\n                        memberID: memberID\n                    }\n                }\n            });\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            if (offline) {\n                var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n                await riskFactorModel.save({\n                    id: currentRisk.id,\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _Array_from1, _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                await riskFactorModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                    vesselID: vesselID\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from1 = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from1 === void 0 ? void 0 : _Array_from1.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                            vesselID: vesselID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    // This function is not used directly in the component but is kept for reference\n    // and potential future use\n    // const handleCreateRisk = (inputValue: any) => {\n    //     setCurrentRisk({\n    //         ...currentRisk,\n    //         title: inputValue,\n    //     })\n    //     setRiskValue({ value: inputValue, label: inputValue })\n    //     if (allRisks) {\n    //         const risk = [...allRisks, { value: inputValue, label: inputValue }]\n    //         setAllRisks(risk)\n    //     } else {\n    //         setAllRisks([{ value: inputValue, label: inputValue }])\n    //     }\n    // }\n    const handleDeleteRisk = async (riskToDelete)=>{\n        if (offline) {\n            var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            await riskFactorModel.save({\n                id: riskToDelete.id,\n                towingChecklistID: 0,\n                vesselID: 0\n            });\n            setOpenRiskDialog(false);\n            const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n            const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data);\n            const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        towingChecklistID: 0,\n                        vesselID: 0\n                    }\n                }\n            });\n        }\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.length > 0) {\n            if (currentStrategies.find((s)=>s.id === strategy.id)) {\n                setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n            } else {\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    strategy\n                ]);\n            }\n        } else {\n            setCurrentStrategies([\n                strategy\n            ]);\n        }\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await mitigationStrategyModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    strategy: content\n                });\n                const newStrategies = [\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ];\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: {\n                        nodes: newStrategies\n                    }\n                });\n                setCurrentStrategies(newStrategies);\n                setContent(\"\");\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const offlineGetLogBookEntryByID = async ()=>{\n        const logbook = await logBookEntryModel.getById(logentryID);\n        handleSetLogbook(logbook);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineGetLogBookEntryByID();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (members && riskAnalysis) {\n            const member = members.find((member)=>member.value == riskAnalysis.member.id);\n            setSelectedAuthor(member);\n        }\n    }, [\n        members,\n        riskAnalysis\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAllChecked(fields.every((field)=>field.checked));\n    }, [\n        fields\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    onSidebarClose();\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Towing\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedAuthor,\n                onAuthorChange: (value)=>{\n                    setSelectedAuthor(value);\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editTaskingRisk && edit_risks,\n                canDeleteRisks: editTaskingRisk && delete_risks,\n                onRiskClick: (risk)=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                },\n                onRiskDelete: (risk)=>{\n                    handleDeleteRisk(risk);\n                },\n                setAllChecked: setAllChecked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 975,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskDialog, {\n                open: openRiskDialog,\n                onOpenChange: setOpenRiskDialog,\n                onSave: handleSaveRisk,\n                currentRisk: currentRisk,\n                riskOptions: allRisks || [],\n                riskValue: riskValue,\n                onRiskValueChange: handleRiskValue,\n                riskImpacts: riskImpacts,\n                onRiskImpactChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        impact: value === null || value === void 0 ? void 0 : value.value\n                    }),\n                onRiskProbabilityChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        probability: value\n                    }),\n                currentStrategies: currentStrategies,\n                content: content,\n                onAddStrategyClick: ()=>setOpenRecommendedstrategy(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1044,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.StrategyDialog, {\n                open: openRecommendedstrategy,\n                onOpenChange: setOpenRecommendedstrategy,\n                onSave: handleNewStrategy,\n                currentRisk: currentRisk,\n                recommendedStrategies: recommendedStratagies,\n                currentStrategies: currentStrategies,\n                onStrategySelect: (strategy)=>{\n                    setRecommendedstrategy(strategy);\n                    handleSetCurrentStrategies(strategy);\n                    setCurrentRisk({\n                        ...currentRisk,\n                        mitigationStrategy: strategy\n                    });\n                    setUpdateStrategy(false);\n                },\n                content: content,\n                onEditorChange: handleEditorChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1069,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n        lineNumber: 974,\n        columnNumber: 9\n    }, this);\n}\n_s(RiskAnalysis, \"kTMg8DIdMX+xx1T+4DpMoUOFIJc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = RiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"RiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\n"));

/***/ })

});