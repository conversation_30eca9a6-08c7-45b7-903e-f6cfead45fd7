"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/risk-evaluations/page",{

/***/ "(app-pages-browser)/./src/components/ui/risk-analysis/index.ts":
/*!**************************************************!*\
  !*** ./src/components/ui/risk-analysis/index.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RiskAnalysisSheet: function() { return /* reexport safe */ _risk_analysis_sheet__WEBPACK_IMPORTED_MODULE_0__.RiskAnalysisSheet; },\n/* harmony export */   RiskDialog: function() { return /* reexport safe */ _risk_dialog__WEBPACK_IMPORTED_MODULE_1__.RiskDialog; },\n/* harmony export */   StrategyDialog: function() { return /* reexport safe */ _strategy_dialog__WEBPACK_IMPORTED_MODULE_2__.StrategyDialog; }\n/* harmony export */ });\n/* harmony import */ var _risk_analysis_sheet__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./risk-analysis-sheet */ \"(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx\");\n/* harmony import */ var _risk_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./risk-dialog */ \"(app-pages-browser)/./src/components/ui/risk-analysis/risk-dialog.tsx\");\n/* harmony import */ var _strategy_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./strategy-dialog */ \"(app-pages-browser)/./src/components/ui/risk-analysis/strategy-dialog.tsx\");\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3Jpc2stYW5hbHlzaXMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXFDO0FBQ1I7QUFDSSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy91aS9yaXNrLWFuYWx5c2lzL2luZGV4LnRzPzdiZmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9yaXNrLWFuYWx5c2lzLXNoZWV0J1xyXG5leHBvcnQgKiBmcm9tICcuL3Jpc2stZGlhbG9nJ1xyXG5leHBvcnQgKiBmcm9tICcuL3N0cmF0ZWd5LWRpYWxvZydcclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/ui/risk-analysis/risk-analysis-sheet.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RiskAnalysisSheet: function() { return /* binding */ RiskAnalysisSheet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ RiskAnalysisSheet auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RiskAnalysisSheet(param) {\n    let { open, onOpenChange, onSidebarClose, title, subtitle, checkFields, riskFactors = [], crewMembers = [], selectedAuthor, onAuthorChange, canEdit = true, canDeleteRisks = true, onRiskClick, onAddRiskClick, onRiskDelete, setAllChecked } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if all fields are checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(checkFields.every((field)=>field.checked));\n        }\n    }, [\n        checkFields,\n        setAllChecked\n    ]);\n    const handleRiskClick = (risk)=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onRiskClick) {\n            onRiskClick(risk);\n        }\n    };\n    const handleAddRiskClick = ()=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onAddRiskClick) {\n            onAddRiskClick();\n        }\n    };\n    const handleDeleteRisk = (risk)=>{\n        if (!canEdit || !canDeleteRisks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to delete risks\"\n            });\n            return;\n        }\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const confirmDeleteRisk = ()=>{\n        if (onRiskDelete && riskToDelete) {\n            onRiskDelete(riskToDelete);\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.Sheet, {\n                open: open,\n                onOpenChange: onOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[90vw] sm:w-[60vw]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetTitle, {\n                                children: [\n                                    title,\n                                    \" \",\n                                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-thin\",\n                                        children: subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetBody, {\n                            className: \"grid space-y-0 md:grid-cols-2 gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col min-h-[400px] overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                            className: \"h-full mb-5 border border-dashed border-border rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full p-2 sm:p-5 space-y-2\",\n                                                children: checkFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__.CheckFieldLabel, {\n                                                            id: \"\".concat(field.value, \"-onChangeComplete-\").concat(index),\n                                                            type: \"checkbox\",\n                                                            checked: field.checked,\n                                                            onCheckedChange: (checked)=>{\n                                                                field.handleChange(checked === true);\n                                                            },\n                                                            variant: \"warning\",\n                                                            label: field.label,\n                                                            rightContent: field.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.Popover, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            onClick: (e)=>e.stopPropagation(),\n                                                                            size: \"icon\",\n                                                                            iconOnly: true,\n                                                                            iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"text-curious-blue-900 fill-curious-blue-50\",\n                                                                                size: 24\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 73\n                                                                            }, void 0),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"View description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 251,\n                                                                                columnNumber: 69\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 234,\n                                                                            columnNumber: 65\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 61\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverContent, {\n                                                                        className: \"w-72 p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: field.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 69\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 65\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 61\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 57\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, \"\".concat(index, \"-\").concat(field.name), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 41\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 29\n                                        }, this),\n                                        crewMembers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Who completed risk\",\n                                            htmlFor: \"author\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__.Combobox, {\n                                                id: \"author\",\n                                                options: crewMembers.map((member)=>{\n                                                    var _member_label, _member_label1;\n                                                    return {\n                                                        ...member,\n                                                        profile: member.profile || {\n                                                            firstName: (_member_label = member.label) === null || _member_label === void 0 ? void 0 : _member_label.split(\" \")[0],\n                                                            surname: (_member_label1 = member.label) === null || _member_label1 === void 0 ? void 0 : _member_label1.split(\" \").slice(1).join(\" \"),\n                                                            avatar: member.avatar || member.profileImage\n                                                        }\n                                                    };\n                                                }),\n                                                value: selectedAuthor,\n                                                placeholder: \"Select crew\",\n                                                onChange: (option)=>{\n                                                    if (onAuthorChange) {\n                                                        onAuthorChange(option);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full col-span-1 min-h-[400px] flex flex-col overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                            className: \"h-full border border-dashed border-border mb-4 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full p-2 sm:p-5\",\n                                                children: riskFactors.length > 0 && riskFactors.map((risk)=>{\n                                                    var _risk_mitigationStrategy_nodes, _risk_mitigationStrategy, _risk_mitigationStrategy1;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4 p-3 rounded-md border border-dashed border-border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"cursor-pointer\",\n                                                                onClick: ()=>handleRiskClick(risk),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    label: risk.title,\n                                                                    className: \"font-medium\",\n                                                                    children: (risk === null || risk === void 0 ? void 0 : risk.impact) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                                                                                className: \"w-full leading-tight text-sm font-normal rounded-lg\",\n                                                                                children: [\n                                                                                    \"Impact:\",\n                                                                                    \" \",\n                                                                                    risk.impact\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                                                                                className: \"w-full leading-tight text-sm font-normal rounded-lg\",\n                                                                                children: [\n                                                                                    \"Probability:\",\n                                                                                    \" \",\n                                                                                    risk.probability,\n                                                                                    \"/10\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2.5\",\n                                                                children: [\n                                                                    (risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy = risk.mitigationStrategy) === null || _risk_mitigationStrategy === void 0 ? void 0 : (_risk_mitigationStrategy_nodes = _risk_mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.Popover, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    iconOnly: true,\n                                                                                    iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"text-light-blue-vivid-900 fill-light-blue-vivid-50\",\n                                                                                        size: 24\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 354,\n                                                                                        columnNumber: 73\n                                                                                    }, void 0),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"sr-only\",\n                                                                                        children: \"View strategies\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                    lineNumber: 349,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverContent, {\n                                                                                className: \"w-72 p-3\",\n                                                                                children: risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy1 = risk.mitigationStrategy) === null || _risk_mitigationStrategy1 === void 0 ? void 0 : _risk_mitigationStrategy1.nodes.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: s.strategy\n                                                                                        }\n                                                                                    }, s.id, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 372,\n                                                                                        columnNumber: 73\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        onClick: ()=>handleDeleteRisk(risk),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"sr-only\",\n                                                                            children: \"Delete risk\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 57\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(risk.id, \"-risk-analysis\"), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 45\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            onClick: handleAddRiskClick,\n                                            children: \"Add Risk\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetFooter, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"back\",\n                                        iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 43\n                                        }, void 0),\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"primary\",\n                                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                        onClick: onSidebarClose,\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 198,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: confirmDeleteRisk,\n                actionText: \"Delete\",\n                title: \"Delete Risk\",\n                variant: \"warning\",\n                size: \"lg\",\n                position: \"center\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                    children: \"Are you sure you want to delete this risk? This action cannot be undone.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 430,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskAnalysisSheet, \"9XWJG0EiLnNse9bdjtVYd6HNBsc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = RiskAnalysisSheet;\nvar _c;\n$RefreshReg$(_c, \"RiskAnalysisSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx\n"));

/***/ })

});