"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx":
/*!*****************************************************************!*\
  !*** ./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BarCrossingRiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_barCrossingChecklist__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/barCrossingChecklist */ \"(app-pages-browser)/./src/app/offline/models/barCrossingChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_eventType_BarCrossing__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/eventType_BarCrossing */ \"(app-pages-browser)/./src/app/offline/models/eventType_BarCrossing.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/uniqBy */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqBy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction BarCrossingRiskAnalysis(param) {\n    let { selectedEvent = false, onSidebarClose, barCrossingChecklistID = 0, setBarCrossingChecklistID, offline = false, setAllChecked, crewMembers = false, logBookConfig, currentTrip, open = false, onOpenChange, noSheet = false } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const logentryID = parseInt((_searchParams_get1 = searchParams.get(\"logentryID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : \"0\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [updateStrategy, setUpdateStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recommendedstrategy, setRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [creatingBarCrossingChecklist, setCreatingBarCrossingChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_risks, setEdit_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_risks, setDelete_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editBarCrossingRisk, setEditBarCrossingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const logBookEntryModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const barCrossingChecklistModel = new _app_offline_models_barCrossingChecklist__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const riskFactorModel = new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const crewMemberModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const barCrossingModel = new _app_offline_models_eventType_BarCrossing__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const mitigationStrategyModel = new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const init_permissions = ()=>{\n        if (permissions) {\n            setEdit_risks((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_RISK\", permissions));\n            setDelete_risks((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"DELETE_RISK\", permissions));\n            setEditBarCrossingRisk((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions));\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [getSectionCrewMembers_LogBookEntrySection, { loading: crewMembersLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                ...members || [],\n                ...crewMembers\n            ], \"value\"));\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        if (+master.value > 0) {\n            if (Array.isArray(members)) {\n                setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                    ...members || [],\n                    master\n                ], \"value\"));\n            } else {\n                setMembers([\n                    master\n                ]);\n            }\n        }\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\");\n        if (sections && sections.length > 0) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if (offline) {\n                const data = await crewMemberModel.getByIds(sectionIDs);\n                const crewMembers = data.map((member)=>{\n                    var _member_crewMember_firstName, _member_crewMember_surname;\n                    return {\n                        label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                        value: member.crewMember.id\n                    };\n                });\n                setMembers(Array.isArray(members) ? [\n                    ...members || [],\n                    ...crewMembers\n                ] : crewMembers);\n            } else {\n                getSectionCrewMembers_LogBookEntrySection({\n                    variables: {\n                        filter: {\n                            id: {\n                                in: sectionIDs\n                            }\n                        }\n                    }\n                });\n            }\n        }\n    };\n    if (logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            const mapped = crewMembers.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setMembers(mapped);\n        }\n    }, [\n        crewMembers\n    ]);\n    const handleBCRAFieldChange = (field)=>async (check)=>{\n            if (!editBarCrossingRisk || !edit_risks) {\n                toast({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"You do not have permission to edit this section\"\n                });\n                return;\n            }\n            setRiskBuffer({\n                ...riskBuffer,\n                [field]: check ? \"on\" : \"off\"\n            });\n            if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n                if (offline) {\n                    await barCrossingChecklistModel.save({\n                        id: riskAnalysis.id,\n                        [field]: check\n                    });\n                } else {\n                    updateBarCrossingChecklist({\n                        variables: {\n                            input: {\n                                id: riskAnalysis.id,\n                                [field]: check\n                            }\n                        }\n                    });\n                }\n            }\n        };\n    const [updateBarCrossingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateBarCrossingChecklist, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    // Fix the fields by explicitly adding a \"checked\" key.\n    const fields = [\n        {\n            name: \"StopAssessPlan\",\n            label: \"Stopped, Assessed, Planned\",\n            value: \"stopAssessPlan\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.stopAssessPlan) ? riskBuffer.stopAssessPlan === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.stopAssessPlan,\n            handleChange: handleBCRAFieldChange(\"stopAssessPlan\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Pause before crossing to evaluate conditions and create a detailed crossing plan.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 243,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"CrewBriefing\",\n            label: \"Briefed crew on crossing\",\n            value: \"crewBriefing\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.crewBriefing) ? riskBuffer.crewBriefing === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.crewBriefing,\n            handleChange: handleBCRAFieldChange(\"crewBriefing\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Inform the crew about the crossing plan and any potential hazards.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 260,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"Weather\",\n            label: \"Weather, tide, bar conditions checked as suitable for crossing\",\n            value: \"weather\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.weather) ? riskBuffer.weather === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.weather,\n            handleChange: handleBCRAFieldChange(\"weather\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Verify that weather, tide, and bar conditions are favorable and safe for crossing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 277,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"Stability\",\n            label: \"Adequate stability checked\",\n            value: \"stability\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.stability) ? riskBuffer.stability === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.stability,\n            handleChange: handleBCRAFieldChange(\"stability\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Ensure the vessel is stable enough to handle the crossing without capsizing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 294,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LifeJackets\",\n            label: \"Lifejackets on\",\n            value: \"lifeJackets\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lifeJackets) ? riskBuffer.lifeJackets === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lifeJackets,\n            handleChange: handleBCRAFieldChange(\"lifeJackets\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Instruct crew to wear lifejackets if conditions are rough or challenging.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 311,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"WaterTightness\",\n            label: \"Water tightness checked\",\n            value: \"waterTightness\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.waterTightness) ? riskBuffer.waterTightness === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.waterTightness,\n            handleChange: handleBCRAFieldChange(\"waterTightness\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Confirm that all hatches and doors are secured to prevent water ingress.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 328,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LookoutPosted\",\n            label: \"Lookout posted\",\n            value: \"lookoutPosted\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lookoutPosted) ? riskBuffer.lookoutPosted === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lookoutPosted,\n            handleChange: handleBCRAFieldChange(\"lookoutPosted\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Assign a crew member to watch for hazards or changes in conditions during the crossing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 345,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(fields.every((field)=>field.checked));\n        }\n    }, [\n        fields\n    ]);\n    const offlineGetRiskAnalysis = async ()=>{\n        var _selectedEvent_eventType_BarCrossing;\n        const data = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.barCrossingChecklistID);\n        setRiskAnalysis(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEvent || barCrossingChecklistID > 0) {\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id) > 0 || barCrossingChecklistID > 0) {\n                if (offline) {\n                    offlineGetRiskAnalysis();\n                } else {\n                    var _selectedEvent_eventType_BarCrossing_barCrossingChecklist1, _selectedEvent_eventType_BarCrossing1;\n                    getRiskAnalysis({\n                        variables: {\n                            id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist1 = _selectedEvent_eventType_BarCrossing1.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist1.id\n                        }\n                    });\n                }\n            } else {\n                if (!creatingBarCrossingChecklist) {\n                    setCreatingBarCrossingChecklist(true);\n                }\n            }\n        } else {\n            if (!creatingBarCrossingChecklist) {\n                setCreatingBarCrossingChecklist(true);\n            }\n        }\n    }, [\n        selectedEvent,\n        barCrossingChecklistID\n    ]);\n    const createOfflineBarCrossingChecklist = async ()=>{\n        var _selectedEvent_eventType_BarCrossing;\n        const data = await barCrossingChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)()\n        });\n        setBarCrossingChecklistID(+data.id);\n        if (barCrossingChecklistID > 0 || (selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.id)) {\n            var _selectedEvent_eventType_BarCrossing1;\n            await barCrossingModel.save({\n                id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing1.id,\n                barCrossingChecklistID: +data.id\n            });\n        }\n        const barCrossingChecklistData = await barCrossingChecklistModel.getById(data.id);\n        setRiskAnalysis(barCrossingChecklistData);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (creatingBarCrossingChecklist) {\n            if (offline) {\n                createOfflineBarCrossingChecklist();\n            } else {\n                createBarCrossingChecklist({\n                    variables: {\n                        input: {}\n                    }\n                });\n            }\n        }\n    }, [\n        creatingBarCrossingChecklist\n    ]);\n    const offlineMount = async ()=>{\n        const data = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n        const risks = Array.from(new Set(data.map((risk)=>risk.title))).map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readRiskFactors_nodes;\n            const risks = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title))).map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.BarCrossingChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskAnalysis(data.readOneBarCrossingChecklist);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createBarCrossingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateBarCrossingChecklist, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_BarCrossing;\n            setBarCrossingChecklistID(+data.createBarCrossingChecklist.id);\n            if (barCrossingChecklistID > 0 || (selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.id)) {\n                var _selectedEvent_eventType_BarCrossing1;\n                updateEvent({\n                    variables: {\n                        input: {\n                            id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing1.id,\n                            barCrossingChecklistID: +data.createBarCrossingChecklist.id\n                        }\n                    }\n                });\n            }\n            getRiskAnalysis({\n                variables: {\n                    id: data.createBarCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_BarCrossing, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const updateRiskAnalysisMember = async (memberID)=>{\n        if (!editBarCrossingRisk || !edit_risks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        setRiskBuffer({\n            ...riskBuffer,\n            memberID\n        });\n        if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n            if (offline) {\n                await barCrossingChecklistModel.save({\n                    id: riskAnalysis.id,\n                    memberID\n                });\n            } else {\n                updateBarCrossingChecklist({\n                    variables: {\n                        input: {\n                            id: riskAnalysis.id,\n                            memberID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            if (offline) {\n                var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n                await riskFactorModel.save({\n                    id: currentRisk.id,\n                    type: \"BarCrossingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                });\n                setOpenRiskDialog(false);\n                const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n                const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(riskFactorData);\n                const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id);\n                setRiskAnalysis(barCrossingChecklistData);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"BarCrossingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                await riskFactorModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    type: \"BarCrossingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                    vesselID\n                });\n                setOpenRiskDialog(false);\n                const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n                const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(riskFactorData);\n                const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id);\n                setRiskAnalysis(barCrossingChecklistData);\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"BarCrossingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                            vesselID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateMitigationStrategy, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateRiskFactor, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            const items = Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                }))));\n            setRecommendedStratagies(items);\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const handleCreateRisk = (inputValue)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: inputValue\n        });\n        setRiskValue({\n            value: inputValue,\n            label: inputValue\n        });\n        if (allRisks) {\n            setAllRisks([\n                ...allRisks,\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        } else {\n            setAllRisks([\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        }\n    };\n    const handleDeleteRisk = async ()=>{\n        if (offline) {\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            await riskFactorModel.save({\n                id: riskToDelete.id,\n                barCrossingChecklistID: 0,\n                vesselID: 0\n            });\n            setOpenRiskDialog(false);\n            const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n            const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(riskFactorData);\n            const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id);\n            setRiskAnalysis(barCrossingChecklistData);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        barCrossingChecklistID: 0,\n                        vesselID: 0\n                    }\n                }\n            });\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.find((s)=>s.id === strategy.id)) {\n            setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n        } else {\n            setCurrentStrategies([\n                ...currentStrategies,\n                strategy\n            ]);\n        }\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await mitigationStrategyModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    strategy: content\n                });\n                const newStrategies = [\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ];\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: {\n                        nodes: newStrategies\n                    }\n                });\n                setCurrentStrategies(newStrategies);\n                setContent(\"\");\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const offlineUseEffect = async ()=>{\n        const logbook = await logBookEntryModel.getById(logentryID);\n        handleSetLogbook(logbook);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineUseEffect();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (members && riskAnalysis) {\n            const member = members.find((member)=>member.value == riskAnalysis.member.id);\n            setSelectedAuthor(member);\n        }\n    }, [\n        members,\n        riskAnalysis\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    onSidebarClose();\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Bar Crossing\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedAuthor,\n                onAuthorChange: (value)=>{\n                    setSelectedAuthor(value);\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editBarCrossingRisk && edit_risks,\n                canDeleteRisks: editBarCrossingRisk && delete_risks,\n                onRiskClick: (risk)=>{\n                    if (!editBarCrossingRisk || !edit_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editBarCrossingRisk || !edit_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                },\n                onRiskDelete: (risk)=>{\n                    if (!editBarCrossingRisk || !delete_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to delete risks\"\n                        });\n                        return;\n                    }\n                    setRiskToDelete(risk);\n                    handleDeleteRisk();\n                },\n                setAllChecked: setAllChecked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 895,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskDialog, {\n                open: openRiskDialog,\n                onOpenChange: setOpenRiskDialog,\n                currentRisk: currentRisk,\n                onSave: handleSaveRisk,\n                riskOptions: allRisks,\n                riskValue: riskValue,\n                onRiskValueChange: handleRiskValue,\n                riskImpacts: riskImpacts,\n                onRiskImpactChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        impact: value === null || value === void 0 ? void 0 : value.value\n                    }),\n                onRiskProbabilityChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        probability: value\n                    }),\n                currentStrategies: currentStrategies,\n                content: content,\n                onAddStrategyClick: ()=>setOpenRecommendedstrategy(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 973,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.StrategyDialog, {\n                open: openRecommendedstrategy,\n                onOpenChange: setOpenRecommendedstrategy,\n                onSave: handleNewStrategy,\n                currentRisk: currentRisk,\n                recommendedStrategies: recommendedStratagies,\n                currentStrategies: currentStrategies,\n                onStrategySelect: (strategy)=>{\n                    setRecommendedstrategy(strategy);\n                    handleSetCurrentStrategies(strategy);\n                    setCurrentRisk({\n                        ...currentRisk,\n                        mitigationStrategy: strategy\n                    });\n                    setUpdateStrategy(false);\n                },\n                content: content,\n                onEditorChange: handleEditorChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 998,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n        lineNumber: 894,\n        columnNumber: 9\n    }, this);\n}\n_s(BarCrossingRiskAnalysis, \"FtwdefzqufZudHvYVo2QsVqONSU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = BarCrossingRiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"BarCrossingRiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx\n"));

/***/ })

});