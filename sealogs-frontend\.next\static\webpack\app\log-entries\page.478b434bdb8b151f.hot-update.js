"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/ui/risk-analysis/risk-analysis-sheet.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RiskAnalysisContent: function() { return /* binding */ RiskAnalysisContent; },\n/* harmony export */   RiskAnalysisSheet: function() { return /* binding */ RiskAnalysisSheet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ RiskAnalysisContent,RiskAnalysisSheet auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction RiskAnalysisContent(param) {\n    let { checkFields, riskFactors = [], crewMembers = [], selectedAuthor, onAuthorChange, canEdit = true, canDeleteRisks = true, onRiskClick, onAddRiskClick, onRiskDelete, setAllChecked } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if all fields are checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(checkFields.every((field)=>field.checked));\n        }\n    }, [\n        checkFields,\n        setAllChecked\n    ]);\n    const handleRiskClick = (risk)=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onRiskClick) {\n            onRiskClick(risk);\n        }\n    };\n    const handleAddRiskClick = ()=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onAddRiskClick) {\n            onAddRiskClick();\n        }\n    };\n    const handleDeleteRisk = (risk)=>{\n        if (!canEdit || !canDeleteRisks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to delete risks\"\n            });\n            return;\n        }\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const confirmDeleteRisk = ()=>{\n        if (riskToDelete && onRiskDelete) {\n            onRiskDelete(riskToDelete);\n        }\n        setOpenDeleteConfirmation(false);\n        setRiskToDelete(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid space-y-0 md:grid-cols-2 gap-2.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full flex flex-col min-h-[400px] overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                            className: \"h-full mb-5 border border-dashed border-border rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-2 sm:p-5 space-y-2\",\n                                children: checkFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_4__.CheckFieldLabel, {\n                                                id: \"\".concat(field.value, \"-onChangeComplete-\").concat(index),\n                                                type: \"checkbox\",\n                                                checked: field.checked,\n                                                onCheckedChange: (checked)=>{\n                                                    field.handleChange(checked === true);\n                                                },\n                                                variant: \"warning\",\n                                                label: field.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 37\n                                            }, this),\n                                            field.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 text-sm text-muted-foreground\",\n                                                children: field.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, \"\".concat(index, \"-\").concat(field.name), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full flex flex-col min-h-[400px] overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2 mb-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        label: \"Author\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_6__.Combobox, {\n                                        value: selectedAuthor,\n                                        onSelect: onAuthorChange || (()=>{}),\n                                        onChange: onAuthorChange || (()=>{}),\n                                        options: crewMembers,\n                                        placeholder: \"Select author\",\n                                        disabled: !canEdit\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_8__.ScrollArea, {\n                                className: \"h-full border border-dashed border-border rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full p-2 sm:p-5 space-y-2\",\n                                    children: riskFactors.map((risk, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border border-border rounded-lg hover:bg-muted/50 cursor-pointer\",\n                                            onClick: ()=>handleRiskClick(risk),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: risk.type\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                children: [\n                                                                    \"Impact: \",\n                                                                    risk.impact,\n                                                                    \" | Probability: \",\n                                                                    risk.probability\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            risk.mitigationStrategy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    dangerouslySetInnerHTML: {\n                                                                        __html: risk.mitigationStrategy\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1 ml-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleDeleteRisk(risk);\n                                                            },\n                                                            disabled: !canDeleteRisks,\n                                                            className: \"h-6 w-6 p-0 text-destructive hover:text-destructive\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sr-only\",\n                                                                children: \"Delete risk\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, \"\".concat(index, \"-\").concat(risk.id), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 33\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                onClick: handleAddRiskClick,\n                                children: \"Add Risk\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 258,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_10__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: confirmDeleteRisk,\n                actionText: \"Delete\",\n                title: \"Delete Risk\",\n                variant: \"warning\",\n                size: \"lg\",\n                position: \"center\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_7__.P, {\n                    children: \"Are you sure you want to delete this risk? This action cannot be undone.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 354,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskAnalysisContent, \"9XWJG0EiLnNse9bdjtVYd6HNBsc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = RiskAnalysisContent;\nfunction RiskAnalysisSheet(param) {\n    let { open, onOpenChange, onSidebarClose, title, subtitle, checkFields, riskFactors = [], crewMembers = [], selectedAuthor, onAuthorChange, canEdit = true, canDeleteRisks = true, onRiskClick, onAddRiskClick, onRiskDelete, setAllChecked } = param;\n    _s1();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if all fields are checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(checkFields.every((field)=>field.checked));\n        }\n    }, [\n        checkFields,\n        setAllChecked\n    ]);\n    const handleRiskClick = (risk)=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onRiskClick) {\n            onRiskClick(risk);\n        }\n    };\n    const handleAddRiskClick = ()=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onAddRiskClick) {\n            onAddRiskClick();\n        }\n    };\n    const handleDeleteRisk = (risk)=>{\n        if (!canEdit || !canDeleteRisks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to delete risks\"\n            });\n            return;\n        }\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const confirmDeleteRisk = ()=>{\n        if (onRiskDelete && riskToDelete) {\n            onRiskDelete(riskToDelete);\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_9__.Sheet, {\n            open: open,\n            onOpenChange: onOpenChange,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_9__.SheetContent, {\n                side: \"right\",\n                className: \"w-[90vw] sm:w-[60vw]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_9__.SheetHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_9__.SheetTitle, {\n                            children: [\n                                title,\n                                \" \",\n                                subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-thin\",\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_9__.SheetBody, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RiskAnalysisContent, {\n                            checkFields: checkFields,\n                            riskFactors: riskFactors,\n                            crewMembers: crewMembers,\n                            selectedAuthor: selectedAuthor,\n                            onAuthorChange: onAuthorChange,\n                            canEdit: canEdit,\n                            canDeleteRisks: canDeleteRisks,\n                            onRiskClick: onRiskClick,\n                            onAddRiskClick: onAddRiskClick,\n                            onRiskDelete: onRiskDelete,\n                            setAllChecked: setAllChecked\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_9__.SheetFooter, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 justify-end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"back\",\n                                    iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 43\n                                    }, void 0),\n                                    onClick: ()=>onOpenChange(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"primary\",\n                                    iconLeft: _barrel_optimize_names_ArrowLeft_Check_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    onClick: onSidebarClose,\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 456,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n            lineNumber: 455,\n            columnNumber: 13\n        }, this)\n    }, void 0, false);\n}\n_s1(RiskAnalysisSheet, \"9XWJG0EiLnNse9bdjtVYd6HNBsc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c1 = RiskAnalysisSheet;\nvar _c, _c1;\n$RefreshReg$(_c, \"RiskAnalysisContent\");\n$RefreshReg$(_c1, \"RiskAnalysisSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx\n"));

/***/ })

});