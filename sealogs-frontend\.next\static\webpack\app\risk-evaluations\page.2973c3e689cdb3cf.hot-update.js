"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/risk-evaluations/page",{

/***/ "(app-pages-browser)/./src/components/ui/risk-analysis/index.ts":
/*!**************************************************!*\
  !*** ./src/components/ui/risk-analysis/index.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RiskAnalysisContent: function() { return /* reexport safe */ _risk_analysis_sheet__WEBPACK_IMPORTED_MODULE_0__.RiskAnalysisContent; },\n/* harmony export */   RiskAnalysisSheet: function() { return /* reexport safe */ _risk_analysis_sheet__WEBPACK_IMPORTED_MODULE_0__.RiskAnalysisSheet; },\n/* harmony export */   RiskDialog: function() { return /* reexport safe */ _risk_dialog__WEBPACK_IMPORTED_MODULE_1__.RiskDialog; },\n/* harmony export */   StrategyDialog: function() { return /* reexport safe */ _strategy_dialog__WEBPACK_IMPORTED_MODULE_2__.StrategyDialog; }\n/* harmony export */ });\n/* harmony import */ var _risk_analysis_sheet__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./risk-analysis-sheet */ \"(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx\");\n/* harmony import */ var _risk_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./risk-dialog */ \"(app-pages-browser)/./src/components/ui/risk-analysis/risk-dialog.tsx\");\n/* harmony import */ var _strategy_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./strategy-dialog */ \"(app-pages-browser)/./src/components/ui/risk-analysis/strategy-dialog.tsx\");\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3Jpc2stYW5hbHlzaXMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFxQztBQUNSO0FBQ0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvcmlzay1hbmFseXNpcy9pbmRleC50cz83YmZhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vcmlzay1hbmFseXNpcy1zaGVldCdcclxuZXhwb3J0ICogZnJvbSAnLi9yaXNrLWRpYWxvZydcclxuZXhwb3J0ICogZnJvbSAnLi9zdHJhdGVneS1kaWFsb2cnXHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/ui/risk-analysis/risk-analysis-sheet.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RiskAnalysisContent: function() { return /* binding */ RiskAnalysisContent; },\n/* harmony export */   RiskAnalysisSheet: function() { return /* binding */ RiskAnalysisSheet; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,InfoIcon,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ RiskAnalysisContent,RiskAnalysisSheet auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RiskAnalysisContent(param) {\n    let { checkFields, riskFactors = [], crewMembers = [], selectedAuthor, onAuthorChange, canEdit = true, canDeleteRisks = true, onRiskClick, onAddRiskClick, onRiskDelete, setAllChecked } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if all fields are checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(checkFields.every((field)=>field.checked));\n        }\n    }, [\n        checkFields,\n        setAllChecked\n    ]);\n    const handleRiskClick = (risk)=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onRiskClick) {\n            onRiskClick(risk);\n        }\n    };\n    const handleAddRiskClick = ()=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onAddRiskClick) {\n            onAddRiskClick();\n        }\n    };\n    const handleDeleteRisk = (risk)=>{\n        if (!canEdit || !canDeleteRisks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to delete risks\"\n            });\n            return;\n        }\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const confirmDeleteRisk = ()=>{\n        if (riskToDelete && onRiskDelete) {\n            onRiskDelete(riskToDelete);\n        }\n        setOpenDeleteConfirmation(false);\n        setRiskToDelete(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid space-y-0 md:grid-cols-2 gap-2.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full flex flex-col min-h-[400px] overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                            className: \"h-full mb-5 border border-dashed border-border rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full p-2 sm:p-5 space-y-2\",\n                                children: checkFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__.CheckFieldLabel, {\n                                                id: \"\".concat(field.value, \"-onChangeComplete-\").concat(index),\n                                                type: \"checkbox\",\n                                                checked: field.checked,\n                                                onCheckedChange: (checked)=>{\n                                                    field.handleChange(checked === true);\n                                                },\n                                                variant: \"warning\",\n                                                label: field.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 37\n                                            }, this),\n                                            field.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 text-sm text-muted-foreground\",\n                                                children: field.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, \"\".concat(index, \"-\").concat(field.name), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full flex flex-col min-h-[400px] overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2 mb-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        label: \"Author\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__.Combobox, {\n                                        value: selectedAuthor,\n                                        onSelect: onAuthorChange,\n                                        onChange: onAuthorChange,\n                                        options: crewMembers,\n                                        placeholder: \"Select author\",\n                                        disabled: !canEdit\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                className: \"h-full border border-dashed border-border rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full p-2 sm:p-5 space-y-2\",\n                                    children: riskFactors.map((risk, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border border-border rounded-lg hover:bg-muted/50 cursor-pointer\",\n                                            onClick: ()=>handleRiskClick(risk),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: risk.type\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                children: [\n                                                                    \"Impact: \",\n                                                                    risk.impact,\n                                                                    \" | Probability: \",\n                                                                    risk.probability\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            risk.mitigationStrategy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    dangerouslySetInnerHTML: {\n                                                                        __html: risk.mitigationStrategy\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1 ml-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleDeleteRisk(risk);\n                                                            },\n                                                            disabled: !canDeleteRisks,\n                                                            className: \"h-6 w-6 p-0 text-destructive hover:text-destructive\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sr-only\",\n                                                                children: \"Delete risk\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, \"\".concat(index, \"-\").concat(risk.id), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 33\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                onClick: handleAddRiskClick,\n                                children: \"Add Risk\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 258,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: confirmDeleteRisk,\n                actionText: \"Delete\",\n                title: \"Delete Risk\",\n                variant: \"warning\",\n                size: \"lg\",\n                position: \"center\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                    children: \"Are you sure you want to delete this risk? This action cannot be undone.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 354,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskAnalysisContent, \"9XWJG0EiLnNse9bdjtVYd6HNBsc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = RiskAnalysisContent;\nfunction RiskAnalysisSheet(param) {\n    let { open, onOpenChange, onSidebarClose, title, subtitle, checkFields, riskFactors = [], crewMembers = [], selectedAuthor, onAuthorChange, canEdit = true, canDeleteRisks = true, onRiskClick, onAddRiskClick, onRiskDelete, setAllChecked } = param;\n    _s1();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if all fields are checked\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(checkFields.every((field)=>field.checked));\n        }\n    }, [\n        checkFields,\n        setAllChecked\n    ]);\n    const handleRiskClick = (risk)=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onRiskClick) {\n            onRiskClick(risk);\n        }\n    };\n    const handleAddRiskClick = ()=>{\n        if (!canEdit) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        if (onAddRiskClick) {\n            onAddRiskClick();\n        }\n    };\n    const handleDeleteRisk = (risk)=>{\n        if (!canEdit || !canDeleteRisks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to delete risks\"\n            });\n            return;\n        }\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const confirmDeleteRisk = ()=>{\n        if (onRiskDelete && riskToDelete) {\n            onRiskDelete(riskToDelete);\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.Sheet, {\n                open: open,\n                onOpenChange: onOpenChange,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-[90vw] sm:w-[60vw]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetTitle, {\n                                children: [\n                                    title,\n                                    \" \",\n                                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-thin\",\n                                        children: subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetBody, {\n                            className: \"grid space-y-0 md:grid-cols-2 gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col min-h-[400px] overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                            className: \"h-full mb-5 border border-dashed border-border rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full p-2 sm:p-5 space-y-2\",\n                                                children: checkFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_5__.CheckFieldLabel, {\n                                                            id: \"\".concat(field.value, \"-onChangeComplete-\").concat(index),\n                                                            type: \"checkbox\",\n                                                            checked: field.checked,\n                                                            onCheckedChange: (checked)=>{\n                                                                field.handleChange(checked === true);\n                                                            },\n                                                            variant: \"warning\",\n                                                            label: field.label,\n                                                            rightContent: field.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.Popover, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            onClick: (e)=>e.stopPropagation(),\n                                                                            size: \"icon\",\n                                                                            iconOnly: true,\n                                                                            iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-curious-blue-900 fill-curious-blue-50\",\n                                                                                size: 24\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 501,\n                                                                                columnNumber: 73\n                                                                            }, void 0),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"View description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 69\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 65\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 61\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverContent, {\n                                                                        className: \"w-72 p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: field.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 69\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 65\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 61\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 57\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, \"\".concat(index, \"-\").concat(field.name), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 41\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 29\n                                        }, this),\n                                        crewMembers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            label: \"Who completed risk\",\n                                            htmlFor: \"author\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_7__.Combobox, {\n                                                id: \"author\",\n                                                options: crewMembers.map((member)=>{\n                                                    var _member_label, _member_label1;\n                                                    return {\n                                                        ...member,\n                                                        profile: member.profile || {\n                                                            firstName: (_member_label = member.label) === null || _member_label === void 0 ? void 0 : _member_label.split(\" \")[0],\n                                                            surname: (_member_label1 = member.label) === null || _member_label1 === void 0 ? void 0 : _member_label1.split(\" \").slice(1).join(\" \"),\n                                                            avatar: member.avatar || member.profileImage\n                                                        }\n                                                    };\n                                                }),\n                                                value: selectedAuthor,\n                                                placeholder: \"Select crew\",\n                                                onChange: (option)=>{\n                                                    if (onAuthorChange) {\n                                                        onAuthorChange(option);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full col-span-1 min-h-[400px] flex flex-col overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                                            className: \"h-full border border-dashed border-border mb-4 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full p-2 sm:p-5\",\n                                                children: riskFactors.length > 0 && riskFactors.map((risk)=>{\n                                                    var _risk_mitigationStrategy_nodes, _risk_mitigationStrategy, _risk_mitigationStrategy1;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4 p-3 rounded-md border border-dashed border-border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"cursor-pointer\",\n                                                                onClick: ()=>handleRiskClick(risk),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    label: risk.title,\n                                                                    className: \"font-medium\",\n                                                                    children: (risk === null || risk === void 0 ? void 0 : risk.impact) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                                                                                className: \"w-full leading-tight text-sm font-normal rounded-lg\",\n                                                                                children: [\n                                                                                    \"Impact:\",\n                                                                                    \" \",\n                                                                                    risk.impact\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 582,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                                                                                className: \"w-full leading-tight text-sm font-normal rounded-lg\",\n                                                                                children: [\n                                                                                    \"Probability:\",\n                                                                                    \" \",\n                                                                                    risk.probability,\n                                                                                    \"/10\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 588,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 581,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2.5\",\n                                                                children: [\n                                                                    (risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy = risk.mitigationStrategy) === null || _risk_mitigationStrategy === void 0 ? void 0 : (_risk_mitigationStrategy_nodes = _risk_mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.Popover, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    iconOnly: true,\n                                                                                    iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"text-light-blue-vivid-900 fill-light-blue-vivid-50\",\n                                                                                        size: 24\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 611,\n                                                                                        columnNumber: 73\n                                                                                    }, void 0),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"sr-only\",\n                                                                                        children: \"View strategies\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 618,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                    lineNumber: 606,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 61\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_2__.PopoverContent, {\n                                                                                className: \"w-72 p-3\",\n                                                                                children: risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy1 = risk.mitigationStrategy) === null || _risk_mitigationStrategy1 === void 0 ? void 0 : _risk_mitigationStrategy1.nodes.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        dangerouslySetInnerHTML: {\n                                                                                            __html: s.strategy\n                                                                                        }\n                                                                                    }, s.id, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                        lineNumber: 629,\n                                                                                        columnNumber: 73\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        onClick: ()=>handleDeleteRisk(risk),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"sr-only\",\n                                                                            children: \"Delete risk\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 57\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 49\n                                                            }, this)\n                                                        ]\n                                                    }, \"\".concat(risk.id, \"-risk-analysis\"), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 45\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                            onClick: handleAddRiskClick,\n                                            children: \"Add Risk\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetFooter, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"back\",\n                                        iconLeft: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 43\n                                        }, void 0),\n                                        onClick: ()=>onOpenChange(false),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"primary\",\n                                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_InfoIcon_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                        onClick: onSidebarClose,\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 455,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: confirmDeleteRisk,\n                actionText: \"Delete\",\n                title: \"Delete Risk\",\n                variant: \"warning\",\n                size: \"lg\",\n                position: \"center\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_8__.P, {\n                    children: \"Are you sure you want to delete this risk? This action cannot be undone.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                    lineNumber: 697,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\risk-analysis\\\\risk-analysis-sheet.tsx\",\n                lineNumber: 687,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(RiskAnalysisSheet, \"9XWJG0EiLnNse9bdjtVYd6HNBsc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c1 = RiskAnalysisSheet;\nvar _c, _c1;\n$RefreshReg$(_c, \"RiskAnalysisContent\");\n$RefreshReg$(_c1, \"RiskAnalysisSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/risk-analysis/risk-analysis-sheet.tsx\n"));

/***/ })

});