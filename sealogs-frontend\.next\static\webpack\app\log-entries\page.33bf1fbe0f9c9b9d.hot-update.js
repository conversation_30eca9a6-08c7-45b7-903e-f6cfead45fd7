"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx":
/*!*****************************************************************!*\
  !*** ./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BarCrossingRiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_barCrossingChecklist__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/barCrossingChecklist */ \"(app-pages-browser)/./src/app/offline/models/barCrossingChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_eventType_BarCrossing__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/eventType_BarCrossing */ \"(app-pages-browser)/./src/app/offline/models/eventType_BarCrossing.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/uniqBy */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqBy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction BarCrossingRiskAnalysis(param) {\n    let { selectedEvent = false, onSidebarClose, barCrossingChecklistID = 0, setBarCrossingChecklistID, offline = false, setAllChecked, crewMembers = false, logBookConfig, currentTrip, open = false, onOpenChange, noSheet = false } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const logentryID = parseInt((_searchParams_get1 = searchParams.get(\"logentryID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : \"0\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [updateStrategy, setUpdateStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recommendedstrategy, setRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [creatingBarCrossingChecklist, setCreatingBarCrossingChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_risks, setEdit_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_risks, setDelete_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editBarCrossingRisk, setEditBarCrossingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const logBookEntryModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const barCrossingChecklistModel = new _app_offline_models_barCrossingChecklist__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const riskFactorModel = new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const crewMemberModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const barCrossingModel = new _app_offline_models_eventType_BarCrossing__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const mitigationStrategyModel = new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const init_permissions = ()=>{\n        if (permissions) {\n            setEdit_risks((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_RISK\", permissions));\n            setDelete_risks((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"DELETE_RISK\", permissions));\n            setEditBarCrossingRisk((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions));\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [getSectionCrewMembers_LogBookEntrySection, { loading: crewMembersLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                ...members || [],\n                ...crewMembers\n            ], \"value\"));\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        if (+master.value > 0) {\n            if (Array.isArray(members)) {\n                setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                    ...members || [],\n                    master\n                ], \"value\"));\n            } else {\n                setMembers([\n                    master\n                ]);\n            }\n        }\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\");\n        if (sections && sections.length > 0) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if (offline) {\n                const data = await crewMemberModel.getByIds(sectionIDs);\n                const crewMembers = data.map((member)=>{\n                    var _member_crewMember_firstName, _member_crewMember_surname;\n                    return {\n                        label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                        value: member.crewMember.id\n                    };\n                });\n                setMembers(Array.isArray(members) ? [\n                    ...members || [],\n                    ...crewMembers\n                ] : crewMembers);\n            } else {\n                getSectionCrewMembers_LogBookEntrySection({\n                    variables: {\n                        filter: {\n                            id: {\n                                in: sectionIDs\n                            }\n                        }\n                    }\n                });\n            }\n        }\n    };\n    if (logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            const mapped = crewMembers.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setMembers(mapped);\n        }\n    }, [\n        crewMembers\n    ]);\n    const handleBCRAFieldChange = (field)=>async (check)=>{\n            if (!editBarCrossingRisk || !edit_risks) {\n                toast({\n                    variant: \"destructive\",\n                    title: \"Error\",\n                    description: \"You do not have permission to edit this section\"\n                });\n                return;\n            }\n            setRiskBuffer({\n                ...riskBuffer,\n                [field]: check ? \"on\" : \"off\"\n            });\n            if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n                if (offline) {\n                    await barCrossingChecklistModel.save({\n                        id: riskAnalysis.id,\n                        [field]: check\n                    });\n                } else {\n                    updateBarCrossingChecklist({\n                        variables: {\n                            input: {\n                                id: riskAnalysis.id,\n                                [field]: check\n                            }\n                        }\n                    });\n                }\n            }\n        };\n    const [updateBarCrossingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateBarCrossingChecklist, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    // Fix the fields by explicitly adding a \"checked\" key.\n    const fields = [\n        {\n            name: \"StopAssessPlan\",\n            label: \"Stopped, Assessed, Planned\",\n            value: \"stopAssessPlan\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.stopAssessPlan) ? riskBuffer.stopAssessPlan === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.stopAssessPlan,\n            handleChange: handleBCRAFieldChange(\"stopAssessPlan\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Pause before crossing to evaluate conditions and create a detailed crossing plan.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 244,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"CrewBriefing\",\n            label: \"Briefed crew on crossing\",\n            value: \"crewBriefing\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.crewBriefing) ? riskBuffer.crewBriefing === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.crewBriefing,\n            handleChange: handleBCRAFieldChange(\"crewBriefing\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Inform the crew about the crossing plan and any potential hazards.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 261,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"Weather\",\n            label: \"Weather, tide, bar conditions checked as suitable for crossing\",\n            value: \"weather\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.weather) ? riskBuffer.weather === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.weather,\n            handleChange: handleBCRAFieldChange(\"weather\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Verify that weather, tide, and bar conditions are favorable and safe for crossing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 278,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"Stability\",\n            label: \"Adequate stability checked\",\n            value: \"stability\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.stability) ? riskBuffer.stability === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.stability,\n            handleChange: handleBCRAFieldChange(\"stability\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Ensure the vessel is stable enough to handle the crossing without capsizing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 295,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LifeJackets\",\n            label: \"Lifejackets on\",\n            value: \"lifeJackets\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lifeJackets) ? riskBuffer.lifeJackets === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lifeJackets,\n            handleChange: handleBCRAFieldChange(\"lifeJackets\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Instruct crew to wear lifejackets if conditions are rough or challenging.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 312,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"WaterTightness\",\n            label: \"Water tightness checked\",\n            value: \"waterTightness\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.waterTightness) ? riskBuffer.waterTightness === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.waterTightness,\n            handleChange: handleBCRAFieldChange(\"waterTightness\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Confirm that all hatches and doors are secured to prevent water ingress.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 329,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LookoutPosted\",\n            label: \"Lookout posted\",\n            value: \"lookoutPosted\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lookoutPosted) ? riskBuffer.lookoutPosted === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lookoutPosted,\n            handleChange: handleBCRAFieldChange(\"lookoutPosted\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Assign a crew member to watch for hazards or changes in conditions during the crossing.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 346,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (setAllChecked) {\n            setAllChecked(fields.every((field)=>field.checked));\n        }\n    }, [\n        fields\n    ]);\n    const offlineGetRiskAnalysis = async ()=>{\n        var _selectedEvent_eventType_BarCrossing;\n        const data = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.barCrossingChecklistID);\n        setRiskAnalysis(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEvent || barCrossingChecklistID > 0) {\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id) > 0 || barCrossingChecklistID > 0) {\n                if (offline) {\n                    offlineGetRiskAnalysis();\n                } else {\n                    var _selectedEvent_eventType_BarCrossing_barCrossingChecklist1, _selectedEvent_eventType_BarCrossing1;\n                    getRiskAnalysis({\n                        variables: {\n                            id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist1 = _selectedEvent_eventType_BarCrossing1.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist1.id\n                        }\n                    });\n                }\n            } else {\n                if (!creatingBarCrossingChecklist) {\n                    setCreatingBarCrossingChecklist(true);\n                }\n            }\n        } else {\n            if (!creatingBarCrossingChecklist) {\n                setCreatingBarCrossingChecklist(true);\n            }\n        }\n    }, [\n        selectedEvent,\n        barCrossingChecklistID\n    ]);\n    const createOfflineBarCrossingChecklist = async ()=>{\n        var _selectedEvent_eventType_BarCrossing;\n        const data = await barCrossingChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)()\n        });\n        setBarCrossingChecklistID(+data.id);\n        if (barCrossingChecklistID > 0 || (selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.id)) {\n            var _selectedEvent_eventType_BarCrossing1;\n            await barCrossingModel.save({\n                id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing1.id,\n                barCrossingChecklistID: +data.id\n            });\n        }\n        const barCrossingChecklistData = await barCrossingChecklistModel.getById(data.id);\n        setRiskAnalysis(barCrossingChecklistData);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (creatingBarCrossingChecklist) {\n            if (offline) {\n                createOfflineBarCrossingChecklist();\n            } else {\n                createBarCrossingChecklist({\n                    variables: {\n                        input: {}\n                    }\n                });\n            }\n        }\n    }, [\n        creatingBarCrossingChecklist\n    ]);\n    const offlineMount = async ()=>{\n        const data = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n        const risks = Array.from(new Set(data.map((risk)=>risk.title))).map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readRiskFactors_nodes;\n            const risks = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title))).map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.BarCrossingChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskAnalysis(data.readOneBarCrossingChecklist);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createBarCrossingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateBarCrossingChecklist, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_BarCrossing;\n            setBarCrossingChecklistID(+data.createBarCrossingChecklist.id);\n            if (barCrossingChecklistID > 0 || (selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing.id)) {\n                var _selectedEvent_eventType_BarCrossing1;\n                updateEvent({\n                    variables: {\n                        input: {\n                            id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing1 = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing1 === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing1.id,\n                            barCrossingChecklistID: +data.createBarCrossingChecklist.id\n                        }\n                    }\n                });\n            }\n            getRiskAnalysis({\n                variables: {\n                    id: data.createBarCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_BarCrossing, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const updateRiskAnalysisMember = async (memberID)=>{\n        if (!editBarCrossingRisk || !edit_risks) {\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        setRiskBuffer({\n            ...riskBuffer,\n            memberID\n        });\n        if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n            if (offline) {\n                await barCrossingChecklistModel.save({\n                    id: riskAnalysis.id,\n                    memberID\n                });\n            } else {\n                updateBarCrossingChecklist({\n                    variables: {\n                        input: {\n                            id: riskAnalysis.id,\n                            memberID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            if (offline) {\n                var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n                await riskFactorModel.save({\n                    id: currentRisk.id,\n                    type: \"BarCrossingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                });\n                setOpenRiskDialog(false);\n                const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n                const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(riskFactorData);\n                const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id);\n                setRiskAnalysis(barCrossingChecklistData);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"BarCrossingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                await riskFactorModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    type: \"BarCrossingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                    vesselID\n                });\n                setOpenRiskDialog(false);\n                const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n                const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(riskFactorData);\n                const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id);\n                setRiskAnalysis(barCrossingChecklistData);\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"BarCrossingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) || \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            barCrossingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                            vesselID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateMitigationStrategy, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateRiskFactor, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"BarCrossingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            const items = Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                }))));\n            setRecommendedStratagies(items);\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const handleCreateRisk = (inputValue)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: inputValue\n        });\n        setRiskValue({\n            value: inputValue,\n            label: inputValue\n        });\n        if (allRisks) {\n            setAllRisks([\n                ...allRisks,\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        } else {\n            setAllRisks([\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        }\n    };\n    const handleDeleteRisk = async ()=>{\n        if (offline) {\n            var _selectedEvent_eventType_BarCrossing_barCrossingChecklist, _selectedEvent_eventType_BarCrossing;\n            await riskFactorModel.save({\n                id: riskToDelete.id,\n                barCrossingChecklistID: 0,\n                vesselID: 0\n            });\n            setOpenRiskDialog(false);\n            const riskFactorData = await riskFactorModel.getByFieldID(\"type\", \"BarCrossingChecklist\");\n            const risks = Array.from(new Set(riskFactorData.map((risk)=>risk.title))).map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(riskFactorData);\n            const barCrossingChecklistData = await barCrossingChecklistModel.getById(barCrossingChecklistID > 0 ? barCrossingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing = selectedEvent.eventType_BarCrossing) === null || _selectedEvent_eventType_BarCrossing === void 0 ? void 0 : (_selectedEvent_eventType_BarCrossing_barCrossingChecklist = _selectedEvent_eventType_BarCrossing.barCrossingChecklist) === null || _selectedEvent_eventType_BarCrossing_barCrossingChecklist === void 0 ? void 0 : _selectedEvent_eventType_BarCrossing_barCrossingChecklist.id);\n            setRiskAnalysis(barCrossingChecklistData);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        barCrossingChecklistID: 0,\n                        vesselID: 0\n                    }\n                }\n            });\n        }\n        setOpenDeleteConfirmation(false);\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.find((s)=>s.id === strategy.id)) {\n            setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n        } else {\n            setCurrentStrategies([\n                ...currentStrategies,\n                strategy\n            ]);\n        }\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await mitigationStrategyModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    strategy: content\n                });\n                const newStrategies = [\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ];\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: {\n                        nodes: newStrategies\n                    }\n                });\n                setCurrentStrategies(newStrategies);\n                setContent(\"\");\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const offlineUseEffect = async ()=>{\n        const logbook = await logBookEntryModel.getById(logentryID);\n        handleSetLogbook(logbook);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineUseEffect();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (members && riskAnalysis) {\n            const member = members.find((member)=>member.value == riskAnalysis.member.id);\n            setSelectedAuthor(member);\n        }\n    }, [\n        members,\n        riskAnalysis\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    onSidebarClose();\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Bar Crossing\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedAuthor,\n                onAuthorChange: (value)=>{\n                    setSelectedAuthor(value);\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editBarCrossingRisk && edit_risks,\n                canDeleteRisks: editBarCrossingRisk && delete_risks,\n                onRiskClick: (risk)=>{\n                    if (!editBarCrossingRisk || !edit_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editBarCrossingRisk || !edit_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                },\n                onRiskDelete: (risk)=>{\n                    if (!editBarCrossingRisk || !delete_risks) {\n                        toast({\n                            variant: \"destructive\",\n                            title: \"Error\",\n                            description: \"You do not have permission to delete risks\"\n                        });\n                        return;\n                    }\n                    setRiskToDelete(risk);\n                    handleDeleteRisk();\n                },\n                setAllChecked: setAllChecked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 896,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskDialog, {\n                open: openRiskDialog,\n                onOpenChange: setOpenRiskDialog,\n                currentRisk: currentRisk,\n                onSave: handleSaveRisk,\n                riskOptions: allRisks,\n                riskValue: riskValue,\n                onRiskValueChange: handleRiskValue,\n                riskImpacts: riskImpacts,\n                onRiskImpactChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        impact: value === null || value === void 0 ? void 0 : value.value\n                    }),\n                onRiskProbabilityChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        probability: value\n                    }),\n                currentStrategies: currentStrategies,\n                content: content,\n                onAddStrategyClick: ()=>setOpenRecommendedstrategy(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 974,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.StrategyDialog, {\n                open: openRecommendedstrategy,\n                onOpenChange: setOpenRecommendedstrategy,\n                onSave: handleNewStrategy,\n                currentRisk: currentRisk,\n                recommendedStrategies: recommendedStratagies,\n                currentStrategies: currentStrategies,\n                onStrategySelect: (strategy)=>{\n                    setRecommendedstrategy(strategy);\n                    handleSetCurrentStrategies(strategy);\n                    setCurrentRisk({\n                        ...currentRisk,\n                        mitigationStrategy: strategy\n                    });\n                    setUpdateStrategy(false);\n                },\n                content: content,\n                onEditorChange: handleEditorChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n                lineNumber: 999,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\bar-crossing-risk-analysis.tsx\",\n        lineNumber: 895,\n        columnNumber: 9\n    }, this);\n}\n_s(BarCrossingRiskAnalysis, \"FtwdefzqufZudHvYVo2QsVqONSU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = BarCrossingRiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"BarCrossingRiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing-risk-analysis.tsx\n"));

/***/ })

});