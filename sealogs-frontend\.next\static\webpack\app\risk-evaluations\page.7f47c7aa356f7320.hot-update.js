"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/risk-evaluations/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/forms/risk-analysis.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/towingChecklist */ \"(app-pages-browser)/./src/app/offline/models/towingChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/uniqBy */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqBy.js\");\n/* harmony import */ var lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n// Import React and hooks\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Risk Analysis components\n\nfunction RiskAnalysis(param) {\n    let { selectedEvent = false, onSidebarClose, logBookConfig, currentTrip, crewMembers = false, towingChecklistID = 0, setTowingChecklistID, offline = false, setAllChecked, open = false, onOpenChange, noSheet = false } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = parseInt((_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : \"0\");\n    var _searchParams_get1;\n    const vesselID = (_searchParams_get1 = searchParams.get(\"vesselID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Using setUpdateStrategy but not reading updateStrategy\n    const [, setUpdateStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Unused state variables commented out\n    // const [strategyEditor, setstrategyEditor] = useState<any>(false)\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Using setRecommendedstrategy but not reading recommendedstrategy\n    const [, setRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_risks, setEdit_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [delete_risks, setDelete_risks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTaskingRisk, setEditTaskingRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const logBookEntryModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const towingChecklistModel = new _app_offline_models_towingChecklist__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const riskFactorModel = new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const crewMemberModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const mitigationStrategyModel = new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_RISK\", permissions)) {\n                setEdit_risks(true);\n            } else {\n                setEdit_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"DELETE_RISK\", permissions)) {\n                setDelete_risks(true);\n            } else {\n                setDelete_risks(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"EDIT_LOGBOOKENTRY_RISK_ANALYSIS\", permissions)) {\n                setEditTaskingRisk(true);\n            } else {\n                setEditTaskingRisk(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const [getSectionCrewMembers_LogBookEntrySection, // Unused loading state\n    {}] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                ...members,\n                ...crewMembers\n            ], \"value\"));\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        if (+master.value > 0) {\n            if (Array.isArray(members)) {\n                setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                    ...members,\n                    master\n                ], \"value\"));\n            } else {\n                setMembers([\n                    master\n                ]);\n            }\n        }\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>{\n            return node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\";\n        });\n        if (sections) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if ((sectionIDs === null || sectionIDs === void 0 ? void 0 : sectionIDs.length) > 0) {\n                if (offline) {\n                    const data = await crewMemberModel.getByIds(sectionIDs);\n                    const crewMembers = data.map((member)=>{\n                        var _member_crewMember_firstName, _member_crewMember_surname;\n                        return {\n                            label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                            value: member.crewMember.id\n                        };\n                    });\n                    if (Array.isArray(members)) {\n                        setMembers(lodash_uniqBy__WEBPACK_IMPORTED_MODULE_15___default()([\n                            ...members,\n                            ...crewMembers\n                        ], \"value\"));\n                    } else {\n                        setMembers(crewMembers);\n                    }\n                } else {\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: {\n                                id: {\n                                    in: sectionIDs\n                                }\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    if (logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            const members = crewMembers.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setMembers(members);\n        }\n    }, [\n        crewMembers\n    ]);\n    const handleTaskingRiskFieldChange = (field)=>async (check)=>{\n            if (!editTaskingRisk || !edit_risks) {\n                toast({\n                    title: \"Permission Error\",\n                    description: \"You do not have permission to edit this section\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setRiskBuffer({\n                ...riskBuffer,\n                [field]: check ? \"on\" : \"off\"\n            });\n            if (+(riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id) > 0) {\n                if (offline) {\n                    const data = await towingChecklistModel.save({\n                        id: riskAnalysis.id,\n                        [field]: check ? true : false\n                    });\n                    const towingChecklistData = await towingChecklistModel.getById(data.id);\n                    setRiskAnalysis(towingChecklistData);\n                } else {\n                    updateTowingChecklist({\n                        variables: {\n                            input: {\n                                id: riskAnalysis.id,\n                                [field]: check ? true : false\n                            }\n                        }\n                    });\n                }\n            }\n        };\n    const [updateTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateTowingChecklist, {\n        onCompleted: (data)=>{\n            getRiskAnalysis({\n                variables: {\n                    id: data.updateTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const fields = [\n        {\n            name: \"ConductSAP\",\n            label: \"Conduct SAP\",\n            value: \"conductSAP\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.conductSAP) ? riskBuffer.conductSAP === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.conductSAP,\n            handleChange: handleTaskingRiskFieldChange(\"conductSAP\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Conduct SAP prior to approaching the vessel.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 289,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"InvestigateNatureOfIssue\",\n            label: \"Investigate nature of the issue\",\n            value: \"investigateNatureOfIssue\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.investigateNatureOfIssue) ? riskBuffer.investigateNatureOfIssue === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.investigateNatureOfIssue,\n            handleChange: handleTaskingRiskFieldChange(\"investigateNatureOfIssue\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ascertain the nature of the problem, any damage, or taking on water.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Does a crew member need to go on board the other vessel to assist?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 309,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"EveryoneOnBoardOk\",\n            label: \"Everyone on board ok?\",\n            value: \"everyoneOnBoardOk\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.everyoneOnBoardOk) ? riskBuffer.everyoneOnBoardOk === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.everyoneOnBoardOk,\n            handleChange: handleTaskingRiskFieldChange(\"everyoneOnBoardOk\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check how many people are aboard, ensure everyone is accounted for.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check for injuries or medical assistance required.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 330,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"RudderToMidshipsAndTrimmed\",\n            label: \"Rudder to midships and trimmed appropriately\",\n            value: \"rudderToMidshipsAndTrimmed\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.rudderToMidshipsAndTrimmed) ? riskBuffer.rudderToMidshipsAndTrimmed === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.rudderToMidshipsAndTrimmed,\n            handleChange: handleTaskingRiskFieldChange(\"rudderToMidshipsAndTrimmed\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check steering isn’t impaired in any way and have the rudder secured amidships or have the vessel steer for the stern of CRV.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Check the vessel is optimally trimmed for towing.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 352,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"LifejacketsOn\",\n            label: \"Lifejackets on\",\n            value: \"lifejacketsOn\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.lifejacketsOn) ? riskBuffer.lifejacketsOn === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.lifejacketsOn,\n            handleChange: handleTaskingRiskFieldChange(\"lifejacketsOn\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Request that everyone wears a lifejacket.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 371,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"CommunicationsEstablished\",\n            label: \"Communications Established\",\n            value: \"communicationsEstablished\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.communicationsEstablished) ? riskBuffer.communicationsEstablished === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.communicationsEstablished,\n            handleChange: handleTaskingRiskFieldChange(\"communicationsEstablished\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that communications have been established and checked prior to beginning the tow, i.e., VHF, hand signals, and/or light signals if the tow is to be conducted at night.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure there is agreement on where to tow the vessel to.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 387,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            name: \"SecureAndSafeTowing\",\n            label: \"Secure and safe towing\",\n            value: \"secureAndSafeTowing\",\n            checked: (riskBuffer === null || riskBuffer === void 0 ? void 0 : riskBuffer.secureAndSafeTowing) ? riskBuffer.secureAndSafeTowing === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.secureAndSafeTowing,\n            handleChange: handleTaskingRiskFieldChange(\"secureAndSafeTowing\"),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Towline securely attached\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure everything on board is stowed and secure.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm waterline length/cruising speed of the vessel (safe tow speed).\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm attachment points for the towline.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Confirm that the towline is securely attached.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Ensure that no one on the other vessel is in close proximity to the towline before commencing the tow.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Turn on CRV towing lights and other vessel’s navigation lights.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Post towline lookout with responsibility for quick release of the tow / must carry or have a knife handy.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 409,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    const createOfflineTowingChecklist = async ()=>{\n        var _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setTowingChecklistID(+data.id);\n        await taskingModel.save({\n            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n            towingChecklistID: +data.id\n        });\n        const towingChecklistData = await towingChecklistModel.getById(data.id);\n        setRiskAnalysis(towingChecklistData);\n    };\n    const offlineGetRiskAnalysis = async ()=>{\n        var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n        const data = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n        setRiskAnalysis(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEvent || towingChecklistID > 0) {\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id) > 0 || towingChecklistID > 0) {\n                if (offline) {\n                    offlineGetRiskAnalysis();\n                } else {\n                    var _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                    getRiskAnalysis({\n                        variables: {\n                            id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id\n                        }\n                    });\n                }\n            } else {\n                if (offline) {\n                    createOfflineTowingChecklist();\n                } else {\n                    createTowingChecklist({\n                        variables: {\n                            input: {}\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        selectedEvent,\n        towingChecklistID\n    ]);\n    const offlineMount = async ()=>{\n        var _Array_from;\n        const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n        const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _Array_from, _data_readRiskFactors_nodes;\n            const risks = (_Array_from = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.TowingChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setRiskAnalysis(data.readOneTowingChecklist);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createTowingChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateTowingChecklist, {\n        onCompleted: (data)=>{\n            var _selectedEvent_eventType_Tasking;\n            setTowingChecklistID(+data.createTowingChecklist.id);\n            updateEvent({\n                variables: {\n                    input: {\n                        id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : _selectedEvent_eventType_Tasking.id,\n                        towingChecklistID: +data.createTowingChecklist.id\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: data.createTowingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_Tasking, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const updateRiskAnalysisMember = async (memberID)=>{\n        if (!editTaskingRisk || !edit_risks) {\n            toast({\n                title: \"Permission Error\",\n                description: \"You do not have permission to edit this section\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (offline) {\n            const data = await towingChecklistModel.save({\n                id: riskAnalysis.id,\n                memberID: memberID\n            });\n            const towingChecklistData = await towingChecklistModel.getById(data.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateTowingChecklist({\n                variables: {\n                    input: {\n                        id: riskAnalysis.id,\n                        memberID: memberID\n                    }\n                }\n            });\n        }\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            if (offline) {\n                var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n                await riskFactorModel.save({\n                    id: currentRisk.id,\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _Array_from1, _selectedEvent_eventType_Tasking_towingChecklist1, _selectedEvent_eventType_Tasking1;\n                await riskFactorModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    type: \"TowingChecklist\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                    vesselID: vesselID\n                });\n                setOpenRiskDialog(false);\n                const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n                const risks = (_Array_from1 = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from1 === void 0 ? void 0 : _Array_from1.map((risk)=>({\n                        label: risk,\n                        value: risk\n                    }));\n                setAllRisks(risks);\n                setAllRiskFactors(data);\n                const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking1 = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking1 === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist1 = _selectedEvent_eventType_Tasking1.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist1 === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist1.id);\n                setRiskAnalysis(towingChecklistData);\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"TowingChecklist\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            towingChecklistID: riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.id,\n                            vesselID: vesselID\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            var _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"TowingChecklist\"\n                        }\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    // This function is not used directly in the component but is kept for reference\n    // and potential future use\n    // const handleCreateRisk = (inputValue: any) => {\n    //     setCurrentRisk({\n    //         ...currentRisk,\n    //         title: inputValue,\n    //     })\n    //     setRiskValue({ value: inputValue, label: inputValue })\n    //     if (allRisks) {\n    //         const risk = [...allRisks, { value: inputValue, label: inputValue }]\n    //         setAllRisks(risk)\n    //     } else {\n    //         setAllRisks([{ value: inputValue, label: inputValue }])\n    //     }\n    // }\n    const handleDeleteRisk = async (riskToDelete)=>{\n        if (offline) {\n            var _Array_from, _selectedEvent_eventType_Tasking_towingChecklist, _selectedEvent_eventType_Tasking;\n            await riskFactorModel.save({\n                id: riskToDelete.id,\n                towingChecklistID: 0,\n                vesselID: 0\n            });\n            setOpenRiskDialog(false);\n            const data = await riskFactorModel.getByFieldID(\"type\", \"TowingChecklist\");\n            const risks = (_Array_from = Array.from(new Set(data.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data);\n            const towingChecklistData = await towingChecklistModel.getById(towingChecklistID > 0 ? towingChecklistID : selectedEvent === null || selectedEvent === void 0 ? void 0 : (_selectedEvent_eventType_Tasking = selectedEvent.eventType_Tasking) === null || _selectedEvent_eventType_Tasking === void 0 ? void 0 : (_selectedEvent_eventType_Tasking_towingChecklist = _selectedEvent_eventType_Tasking.towingChecklist) === null || _selectedEvent_eventType_Tasking_towingChecklist === void 0 ? void 0 : _selectedEvent_eventType_Tasking_towingChecklist.id);\n            setRiskAnalysis(towingChecklistData);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        towingChecklistID: 0,\n                        vesselID: 0\n                    }\n                }\n            });\n        }\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.length > 0) {\n            if (currentStrategies.find((s)=>s.id === strategy.id)) {\n                setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n            } else {\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    strategy\n                ]);\n            }\n        } else {\n            setCurrentStrategies([\n                strategy\n            ]);\n        }\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await mitigationStrategyModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    strategy: content\n                });\n                const newStrategies = [\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ];\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: {\n                        nodes: newStrategies\n                    }\n                });\n                setCurrentStrategies(newStrategies);\n                setContent(\"\");\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const offlineGetLogBookEntryByID = async ()=>{\n        const logbook = await logBookEntryModel.getById(logentryID);\n        handleSetLogbook(logbook);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineGetLogBookEntryByID();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (members && riskAnalysis) {\n            const member = members.find((member)=>member.value == riskAnalysis.member.id);\n            setSelectedAuthor(member);\n        }\n    }, [\n        members,\n        riskAnalysis\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAllChecked(fields.every((field)=>field.checked));\n    }, [\n        fields\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    onSidebarClose();\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Towing\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedAuthor,\n                onAuthorChange: (value)=>{\n                    setSelectedAuthor(value);\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editTaskingRisk && edit_risks,\n                canDeleteRisks: editTaskingRisk && delete_risks,\n                onRiskClick: (risk)=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editTaskingRisk || !edit_risks) {\n                        toast({\n                            title: \"Permission Error\",\n                            description: \"You do not have permission to edit this section\",\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                },\n                onRiskDelete: (risk)=>{\n                    handleDeleteRisk(risk);\n                },\n                setAllChecked: setAllChecked\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 978,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.RiskDialog, {\n                open: openRiskDialog,\n                onOpenChange: setOpenRiskDialog,\n                onSave: handleSaveRisk,\n                currentRisk: currentRisk,\n                riskOptions: allRisks || [],\n                riskValue: riskValue,\n                onRiskValueChange: handleRiskValue,\n                riskImpacts: riskImpacts,\n                onRiskImpactChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        impact: value === null || value === void 0 ? void 0 : value.value\n                    }),\n                onRiskProbabilityChange: (value)=>setCurrentRisk({\n                        ...currentRisk,\n                        probability: value\n                    }),\n                currentStrategies: currentStrategies,\n                content: content,\n                onAddStrategyClick: ()=>setOpenRecommendedstrategy(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1047,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_16__.StrategyDialog, {\n                open: openRecommendedstrategy,\n                onOpenChange: setOpenRecommendedstrategy,\n                onSave: handleNewStrategy,\n                currentRisk: currentRisk,\n                recommendedStrategies: recommendedStratagies,\n                currentStrategies: currentStrategies,\n                onStrategySelect: (strategy)=>{\n                    setRecommendedstrategy(strategy);\n                    handleSetCurrentStrategies(strategy);\n                    setCurrentRisk({\n                        ...currentRisk,\n                        mitigationStrategy: strategy\n                    });\n                    setUpdateStrategy(false);\n                },\n                content: content,\n                onEditorChange: handleEditorChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n                lineNumber: 1072,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\risk-analysis.tsx\",\n        lineNumber: 977,\n        columnNumber: 9\n    }, this);\n}\n_s(RiskAnalysis, \"kTMg8DIdMX+xx1T+4DpMoUOFIJc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = RiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"RiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/risk-analysis.tsx\n"));

/***/ })

});